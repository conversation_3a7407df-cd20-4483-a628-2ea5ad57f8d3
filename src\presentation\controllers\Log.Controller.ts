import { FastifyReply, FastifyRequest } from "fastify";

// Import the Log Service.
import { LogService } from "../../application/services/Log.Service.js";

export class LogController {
  // Constructor
  constructor(private readonly logService: LogService) {}

  // Create a new log entry
  async create(req: FastifyRequest, reply: FastifyReply) {
    try {
      // Now uses automatic IP, UA parsing, and device detection
      const log = await this.logService.createLogFromRequest(
        req,
        "Manual log entry created"
      );

      reply.code(201).send(log);
    } catch (error) {
      reply.code(500).send({ error: "Failed to create log entry" });
    }
  }
}