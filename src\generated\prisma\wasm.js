
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.14.0
 * Query Engine version: 717184b7b35ea05dfa71a3236b7af656013e1e49
 */
Prisma.prismaVersion = {
  client: "6.14.0",
  engine: "717184b7b35ea05dfa71a3236b7af656013e1e49"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable',
  Snapshot: 'Snapshot'
});

exports.Prisma.PTEIUserScalarFieldEnum = {
  UserId: 'UserId',
  email: 'email',
  HashedPassword: 'HashedPassword',
  FirstName: 'FirstName',
  LastName: 'LastName',
  UserProfilePicPath: 'UserProfilePicPath',
  UserType: 'UserType',
  AccountStatus: 'AccountStatus',
  IsAccountLockedOut: 'IsAccountLockedOut',
  FailedLoginAttempts: 'FailedLoginAttempts',
  IsUserLoggedIn: 'IsUserLoggedIn',
  UserCreatedAt: 'UserCreatedAt',
  UserModifiedAt: 'UserModifiedAt',
  EmailVerificationToken: 'EmailVerificationToken',
  EmailVerificationTokenExpiry: 'EmailVerificationTokenExpiry',
  PasswordResetToken: 'PasswordResetToken',
  PasswordResetTokenExpiry: 'PasswordResetTokenExpiry',
  RoleId: 'RoleId',
  DepartmentId: 'DepartmentId',
  TIN: 'TIN',
  OtherName: 'OtherName',
  PhoneContact: 'PhoneContact',
  Title: 'Title',
  Gender: 'Gender',
  Ethnicity: 'Ethnicity',
  DateOfBirth: 'DateOfBirth',
  ResidentialAddress: 'ResidentialAddress',
  PostalAddress: 'PostalAddress',
  Province: 'Province',
  TSLSScheme: 'TSLSScheme'
};

exports.Prisma.RoleScalarFieldEnum = {
  RoleId: 'RoleId',
  RoleName: 'RoleName',
  Description: 'Description',
  IsActive: 'IsActive',
  CreatedAt: 'CreatedAt'
};

exports.Prisma.DepartmentScalarFieldEnum = {
  DepartmentId: 'DepartmentId',
  DepartmentName: 'DepartmentName',
  Description: 'Description',
  IsActive: 'IsActive',
  CreatedAt: 'CreatedAt'
};

exports.Prisma.PermissionScalarFieldEnum = {
  PermissionId: 'PermissionId',
  Name: 'Name',
  Description: 'Description'
};

exports.Prisma.RolePermissionScalarFieldEnum = {
  RoleId: 'RoleId',
  PermissionId: 'PermissionId'
};

exports.Prisma.UniversityScalarFieldEnum = {
  UniversityId: 'UniversityId',
  UniversityName: 'UniversityName',
  PrimaryEmail: 'PrimaryEmail',
  CCEmails: 'CCEmails',
  BCCEmails: 'BCCEmails',
  ContactPerson: 'ContactPerson',
  Phone: 'Phone',
  IsActive: 'IsActive',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt'
};

exports.Prisma.UniversityUserScalarFieldEnum = {
  UniversityUserId: 'UniversityUserId',
  CreatedAt: 'CreatedAt',
  IsActive: 'IsActive',
  UserId: 'UserId',
  UniversityId: 'UniversityId'
};

exports.Prisma.LogScalarFieldEnum = {
  id: 'id',
  description: 'description',
  ipAddress: 'ipAddress',
  timeStamp: 'timeStamp',
  deviceInfo: 'deviceInfo',
  browserInfo: 'browserInfo',
  actionType: 'actionType',
  pTEIUserUserId: 'pTEIUserUserId'
};

exports.Prisma.TslsToUniDropboxScalarFieldEnum = {
  TslsToUniDropboxId: 'TslsToUniDropboxId',
  TslsToUniDropboxName: 'TslsToUniDropboxName',
  UniversityId: 'UniversityId',
  Year: 'Year',
  ModeOfStudy: 'ModeOfStudy',
  Term: 'Term',
  BatchNumber: 'BatchNumber',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt',
  OpeningDate: 'OpeningDate',
  ClosingDate: 'ClosingDate',
  IsOpenStatus: 'IsOpenStatus',
  ReportType: 'ReportType'
};

exports.Prisma.TslsToUniReportSubmissionScalarFieldEnum = {
  TslsToUniReportSubmissionId: 'TslsToUniReportSubmissionId',
  TslsToUniReportSubmissionName: 'TslsToUniReportSubmissionName',
  TslsToUniReportSubmissionFilePath: 'TslsToUniReportSubmissionFilePath',
  SubmittedAt: 'SubmittedAt',
  Notes: 'Notes',
  SubmittedBy: 'SubmittedBy',
  TslsToUniDropboxId: 'TslsToUniDropboxId',
  BatchNumber: 'BatchNumber',
  Status: 'Status'
};

exports.Prisma.UniToTslsDropboxScalarFieldEnum = {
  UniToTslsDropboxId: 'UniToTslsDropboxId',
  UniToTslsDropboxName: 'UniToTslsDropboxName',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt',
  OpeningDate: 'OpeningDate',
  ClosingDate: 'ClosingDate',
  IsOpen: 'IsOpen',
  ReportType: 'ReportType',
  UniversityId: 'UniversityId'
};

exports.Prisma.UniToTslsReportSubmissionScalarFieldEnum = {
  UniToTslsReportSubmissionId: 'UniToTslsReportSubmissionId',
  UniToTslsReportSubmissionName: 'UniToTslsReportSubmissionName',
  UniToTslsReportSubmissionFilePath: 'UniToTslsReportSubmissionFilePath',
  SubmittedAt: 'SubmittedAt',
  Notes: 'Notes',
  SubmittedBy: 'SubmittedBy',
  UniToTslsDropboxId: 'UniToTslsDropboxId',
  BatchNumber: 'BatchNumber',
  Status: 'Status'
};

exports.Prisma.ReportSettingsScalarFieldEnum = {
  ReportSettingsId: 'ReportSettingsId',
  ReportType: 'ReportType',
  ReportOpeningDate: 'ReportOpeningDate',
  ReportClosingDate: 'ReportClosingDate',
  IsActive: 'IsActive',
  CreatedBy: 'CreatedBy',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt'
};

exports.Prisma.EmailNotificationScalarFieldEnum = {
  EmailNotificationId: 'EmailNotificationId',
  RecipientEmail: 'RecipientEmail',
  Subject: 'Subject',
  Body: 'Body',
  SentAt: 'SentAt',
  Status: 'Status',
  NotificationType: 'NotificationType',
  RelatedEntityId: 'RelatedEntityId',
  RelatedEntityType: 'RelatedEntityType'
};

exports.Prisma.EmailTemplateScalarFieldEnum = {
  EmailTemplateId: 'EmailTemplateId',
  TemplateName: 'TemplateName',
  TemplateSubject: 'TemplateSubject',
  TemplateBody: 'TemplateBody',
  TemplateType: 'TemplateType',
  IsActive: 'IsActive',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};


exports.Prisma.ModelName = {
  PTEIUser: 'PTEIUser',
  Role: 'Role',
  Department: 'Department',
  Permission: 'Permission',
  RolePermission: 'RolePermission',
  University: 'University',
  UniversityUser: 'UniversityUser',
  Log: 'Log',
  TslsToUniDropbox: 'TslsToUniDropbox',
  TslsToUniReportSubmission: 'TslsToUniReportSubmission',
  UniToTslsDropbox: 'UniToTslsDropbox',
  UniToTslsReportSubmission: 'UniToTslsReportSubmission',
  ReportSettings: 'ReportSettings',
  EmailNotification: 'EmailNotification',
  EmailTemplate: 'EmailTemplate'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
