// User routes
import { FastifyInstance } from 'fastify';
import { UserController } from '../controllers/User.Controller.js';
import { verifyJWT } from '../middleware/auth.js';
import {
  CreateUserDto,
  UpdateUserDto,
  ChangeStatusDto,
  ResetPasswordDto,
  VerifyEmailDto,
  UserDto,
  CreateUserSchema,
  UpdateUserSchema,
  ChangeStatusSchema,
  ResetPasswordSchema,
  VerifyEmailSchema
} from '../../application/dto/user.dto.js';

export default async function userRoutes(fastify: FastifyInstance) {
  const userController = new UserController(fastify.prisma);

  // Swagger schemas for documentation
  const userResponseSchema = {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' },
      data: {
        type: 'object',
        properties: {
          userId: { type: 'string' },
          email: { type: 'string' },
          firstName: { type: 'string' },
          lastName: { type: 'string' },
          roleId: { type: 'string' },
          roleName: { type: 'string' },
          departmentId: { type: 'string' },
          departmentName: { type: 'string' },
          userType: { type: 'string' },
          accountStatus: { type: 'string' },
          userCreatedAt: { type: 'string', format: 'date-time' },
          userModifiedAt: { type: 'string', format: 'date-time' },
          isAccountLockedOut: { type: 'boolean' },
          failedLoginAttempts: { type: 'number' },
          userProfilePicPath: { type: 'string' }
        }
      }
    }
  };

  const errorResponseSchema = {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' },
      errors: {
        type: 'array',
        items: { type: 'string' }
      }
    }
  };

  // Create user
  fastify.post<{ Body: CreateUserDto }>('/users', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Create a new user',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        required: ['email', 'password', 'firstName', 'lastName', 'roleId', 'userType'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            description: 'User email address'
          },
          password: {
            type: 'string',
            minLength: 8,
            description: 'User password (minimum 8 characters)'
          },
          firstName: {
            type: 'string',
            minLength: 1,
            description: 'User first name'
          },
          lastName: {
            type: 'string',
            minLength: 1,
            description: 'User last name'
          },
          departmentId: {
            oneOf: [
              { type: 'string', format: 'uuid' },
              { type: 'null' }
            ],
            description: 'Department ID (optional, can be null for university users)'
          },
          roleId: {
            type: 'string',
            format: 'uuid',
            description: 'Role ID'
          },
          userType: {
            type: 'string',
            enum: ['TSLS', 'University'],
            description: 'Type of user'
          },
          universityId: {
            type: 'string',
            format: 'uuid',
            description: 'University ID (required for University users)'
          }
        }
      },
      response: {
        201: userResponseSchema,
        400: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userController.createUser.bind(userController));

  // Update user
  fastify.put<{ Body: Omit<UpdateUserDto, 'userId'>; Params: { id: string } }>('/users/:id', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Update an existing user',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      body: {
        type: 'object',
        properties: {
          firstName: { type: 'string', minLength: 1 },
          lastName: { type: 'string', minLength: 1 },
          departmentId: { type: 'string', format: 'uuid' },
          roleId: { type: 'string', format: 'uuid' }
        }
      },
      response: {
        200: userResponseSchema,
        400: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userController.updateUser.bind(userController));

  // Change user status
  fastify.patch<{ Body: { newStatus: string }; Params: { id: string } }>('/users/:id/status', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Change user status',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      body: {
        type: 'object',
        properties: {
          newStatus: { type: 'string', minLength: 1 }
        },
        required: ['newStatus']
      },
      response: {
        200: userResponseSchema,
        400: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userController.changeUserStatus.bind(userController));

  // Unlock user account
  fastify.patch<{ Params: { id: string } }>('/users/:id/unlock', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Unlock user account',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: userResponseSchema,
        400: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userController.unlockAccount.bind(userController));

  // Get user by ID
  fastify.get<{ Params: { id: string } }>('/users/:id', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get user by ID',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: userResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userController.getUserById.bind(userController));

  // Get all users
  fastify.get<{ Querystring: { page?: string; limit?: string } }>('/users', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get all users with pagination',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'string', pattern: '^[1-9]\\d*$' },
          limit: { type: 'string', pattern: '^[1-9]\\d*$' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                users: {
                  type: 'array',
                  items: userResponseSchema.properties.data
                },
                pagination: {
                  type: 'object',
                  properties: {
                    page: { type: 'number' },
                    limit: { type: 'number' },
                    total: { type: 'number' },
                    totalPages: { type: 'number' }
                  }
                },
                statistics: {
                  type: 'object',
                  properties: {
                    totalUsers: { type: 'number' },
                    activeUsers: { type: 'number' },
                    inactiveUsers: { type: 'number' },
                    lockedAccounts: { type: 'number' }
                  }
                }
              }
            }
          }
        },
        500: errorResponseSchema
      }
    }
  }, userController.getUsers.bind(userController));

  // Get user statistics
  fastify.get('/users/stats', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get user statistics',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                totalUsers: { type: 'number' },
                activeUsers: { type: 'number' },
                inactiveUsers: { type: 'number' },
                lockedAccounts: { type: 'number' }
              }
            }
          }
        },
        500: errorResponseSchema
      }
    }
  }, userController.getUserStats.bind(userController));

  // Delete user
  fastify.delete<{ Params: { id: string } }>('/users/:id', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Delete user',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userController.deleteUser.bind(userController));

  // Reset password
  fastify.post<{ Body: ResetPasswordDto }>('/users/reset-password', {
    schema: {
      description: 'Reset user password',
      tags: ['Users'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        400: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userController.resetPassword.bind(userController));

  // Verify email
  fastify.post<{ Body: VerifyEmailDto }>('/users/verify-email', {
    schema: {
      description: 'Verify user email',
      tags: ['Users'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        400: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userController.verifyEmail.bind(userController));
}
