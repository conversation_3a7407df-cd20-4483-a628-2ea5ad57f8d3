/* UAParser.js v2.0.5
   Copyright © 2012-2025 <PERSON><PERSON><PERSON> <<EMAIL>>
   AGPLv3 License */
function I(i){for(var e={},o=0;o<i.length;o++)e[i[o].toUpperCase()]=i[o];return e}var P=500,B="user-agent",b="",U="?",L="function",n="undefined",l="object",R="string",c="browser",h="cpu",u="device",p="engine",m="os",f="result",g="name",v="type",k="vendor",x="version",y="architecture",G="major",C="model",W="console",S="mobile",r="tablet",i="smarttv",e="wearable",N="xr",F="embedded",o="inapp",D="brands",_="formFactors",$="fullVersionList",q="platform",X="platformVersion",Y="bitness",t="sec-ch-ua",Z=t+"-full-version-list",K=t+"-arch",Q=t+"-"+Y,J=t+"-form-factors",ii=t+"-"+S,ei=t+"-"+C,oi=t+"-"+q,ti=oi+"-version",ri=[D,$,S,C,q,X,y,_,Y],ai="Amazon",a="Apple",si="ASUS",ni="BlackBerry",s="Google",wi="Huawei",di="Lenovo",bi="Honor",li="Microsoft",ci="Motorola",hi="Nvidia",ui="OnePlus",pi="OPPO",mi="Samsung",fi="Sony",gi="Xiaomi",vi="Zebra",ki="Chromium",w="Chromecast",xi="Edge",yi="Firefox",d="Opera",Ci="Facebook",z="Mobile ",Si=" Browser",_i="Windows",O=typeof window!==n&&window.navigator?window.navigator:void 0,T=O&&O.userAgentData?O.userAgentData:void 0,qi=function(i,e){if(typeof i===l&&0<i.length){for(var o in i)if(M(e)==M(i[o]))return!0;return!1}return!!H(i)&&M(e)==M(i)},zi=function(i,e){for(var o in i)return/^(browser|cpu|device|engine|os)$/.test(o)||!!e&&zi(i[o])},H=function(i){return typeof i===R},Oi=function(i){if(i){for(var e,o=[],t=j(/\\?\"/g,i).split(","),r=0;r<t.length;r++)-1<t[r].indexOf(";")?(e=Mi(t[r]).split(";v="),o[r]={brand:e[0],version:e[1]}):o[r]=Mi(t[r]);return o}},M=function(i){return H(i)?i.toLowerCase():i},Ti=function(i){return H(i)?j(/[^\d\.]/g,i).split(".")[0]:void 0},A=function(i){for(var e in i)i.hasOwnProperty(e)&&(typeof(e=i[e])==l&&2==e.length?this[e[0]]=e[1]:this[e]=void 0);return this},j=function(i,e){return H(e)?e.replace(i,b):e},Hi=function(i){return j(/\\?\"/g,i)},Mi=function(i,e){if(H(i))return i=j(/^\s\s*/,i),typeof e===n?i:i.substring(0,P)},Ai=function(i,e){if(i&&e)for(var o,t,r,a,s,n=0;n<e.length&&!a;){for(var w=e[n],d=e[n+1],b=o=0;b<w.length&&!a&&w[b];)if(a=w[b++].exec(i))for(t=0;t<d.length;t++)s=a[++o],typeof(r=d[t])===l&&0<r.length?2===r.length?typeof r[1]==L?this[r[0]]=r[1].call(this,s):this[r[0]]=r[1]:3<=r.length&&(typeof r[1]!==L||r[1].exec&&r[1].test?3==r.length?this[r[0]]=s?s.replace(r[1],r[2]):void 0:4==r.length?this[r[0]]=s?r[3].call(this,s.replace(r[1],r[2])):void 0:4<r.length&&(this[r[0]]=s?r[3].apply(this,[s.replace(r[1],r[2])].concat(r.slice(4))):void 0):3<r.length?this[r[0]]=s?r[1].apply(this,r.slice(2)):void 0:this[r[0]]=s?r[1].call(this,s,r[2]):void 0):this[r]=s||void 0;n+=2}},E=function(i,e){for(var o in e)if(typeof e[o]===l&&0<e[o].length){for(var t=0;t<e[o].length;t++)if(qi(e[o][t],i))return o===U?void 0:o}else if(qi(e[o],i))return o===U?void 0:o;return e.hasOwnProperty("*")?e["*"]:i},ji={ME:"4.90","NT 3.51":"3.51","NT 4.0":"4.0",2e3:["5.0","5.01"],XP:["5.1","5.2"],Vista:"6.0",7:"6.1",8:"6.2",8.1:"6.3",10:["6.4","10.0"],NT:""},Ei={embedded:"Automotive",mobile:"Mobile",tablet:["Tablet","EInk"],smarttv:"TV",wearable:"Watch",xr:["VR","XR"],"?":["Desktop","Unknown"],"*":void 0},Vi={Chrome:"Google Chrome",Edge:"Microsoft Edge","Edge WebView2":"Microsoft Edge WebView2","Chrome WebView":"Android WebView","Chrome Headless":"HeadlessChrome","Huawei Browser":"HuaweiBrowser","MIUI Browser":"Miui Browser","Opera Mobi":"OperaMobile",Yandex:"YaBrowser"},Ii={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[x,[g,z+"Chrome"]],[/webview.+edge\/([\w\.]+)/i],[x,[g,xi+" WebView"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[x,[g,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[g,x],[/opios[\/ ]+([\w\.]+)/i],[x,[g,d+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[x,[g,d+" GX"]],[/\bopr\/([\w\.]+)/i],[x,[g,d]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[x,[g,"Baidu"]],[/\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i],[x,[g,"Maxthon"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon|otter|dooble|(?:lg |qute)browser)\/([-\w\.]+)/i,/(heytap|ovi|115|surf)browser\/([\d\.]+)/i,/(ecosia|weibo)(?:__| \w+@)([\d\.]+)/i],[g,x],[/quark(?:pc)?\/([-\w\.]+)/i],[x,[g,"Quark"]],[/\bddg\/([\w\.]+)/i],[x,[g,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[x,[g,"UCBrowser"]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[x,[g,"WeChat"]],[/konqueror\/([\w\.]+)/i],[x,[g,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[x,[g,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[x,[g,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[x,[g,"Smart "+di+Si]],[/(avast|avg)\/([\w\.]+)/i],[[g,/(.+)/,"$1 Secure"+Si],x],[/\bfocus\/([\w\.]+)/i],[x,[g,yi+" Focus"]],[/\bopt\/([\w\.]+)/i],[x,[g,d+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[x,[g,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[x,[g,"Dolphin"]],[/coast\/([\w\.]+)/i],[x,[g,d+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[x,[g,"MIUI"+Si]],[/fxios\/([\w\.-]+)/i],[x,[g,z+yi]],[/\bqihoobrowser\/?([\w\.]*)/i],[x,[g,"360"]],[/\b(qq)\/([\w\.]+)/i],[[g,/(.+)/,"$1Browser"],x],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[g,/(.+)/,"$1"+Si],x],[/samsungbrowser\/([\w\.]+)/i],[x,[g,mi+" Internet"]],[/metasr[\/ ]?([\d\.]+)/i],[x,[g,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[g,"Sogou Mobile"],x],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i],[g,x],[/(lbbrowser|rekonq)/i],[g],[/ome\/([\w\.]+) \w* ?(iron) saf/i,/ome\/([\w\.]+).+qihu (360)[es]e/i],[x,g],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[g,Ci],x,[v,o]],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/(daum)apps[\/ ]([\w\.]+)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(instagram|snapchat|klarna)[\/ ]([-\w\.]+)/i],[g,x,[v,o]],[/\bgsa\/([\w\.]+) .*safari\//i],[x,[g,"GSA"],[v,o]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[x,[g,"TikTok"],[v,o]],[/\[(linkedin)app\]/i],[g,[v,o]],[/(zalo(?:app)?)[\/\sa-z]*([\w\.-]+)/i],[[g,/(.+)/,"Zalo"],x,[v,o]],[/(chromium)[\/ ]([-\w\.]+)/i],[g,x],[/headlesschrome(?:\/([\w\.]+)| )/i],[x,[g,"Chrome Headless"]],[/wv\).+chrome\/([\w\.]+).+edgw\//i],[x,[g,xi+" WebView2"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[g,"Chrome WebView"],x],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[x,[g,"Android"+Si]],[/chrome\/([\w\.]+) mobile/i],[x,[g,z+"Chrome"]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[g,x],[/version\/([\w\.\,]+) .*mobile(?:\/\w+ | ?)safari/i],[x,[g,z+"Safari"]],[/iphone .*mobile(?:\/\w+ | ?)safari/i],[[g,z+"Safari"]],[/version\/([\w\.\,]+) .*(safari)/i],[x,g],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[g,[x,"1"]],[/(webkit|khtml)\/([\w\.]+)/i],[g,x],[/(?:mobile|tablet);.*(firefox)\/([\w\.-]+)/i],[[g,z+yi],x],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[g,"Netscape"],x],[/(wolvic|librewolf)\/([\w\.]+)/i],[g,x],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[x,[g,yi+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(amaya|dillo|doris|icab|ladybird|lynx|mosaic|netsurf|obigo|polaris|w3m|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/\b(links) \(([\w\.]+)/i],[g,[x,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[g,[x,/[^\d\.]+./,b]]],cpu:[[/\b((amd|x|x86[-_]?|wow|win)64)\b/i],[[y,"amd64"]],[/(ia32(?=;))/i,/\b((i[346]|x)86)(pc)?\b/i],[[y,"ia32"]],[/\b(aarch64|arm(v?[89]e?l?|_?64))\b/i],[[y,"arm64"]],[/\b(arm(v[67])?ht?n?[fl]p?)\b/i],[[y,"armhf"]],[/( (ce|mobile); ppc;|\/[\w\.]+arm\b)/i],[[y,"arm"]],[/ sun4\w[;\)]/i],[[y,"sparc"]],[/\b(avr32|ia64(?=;)|68k(?=\))|\barm(?=v([1-7]|[5-7]1)l?|;|eabi)|(irix|mips|sparc)(64)?\b|pa-risc)/i,/((ppc|powerpc)(64)?)( mac|;|\))/i,/(?:osf1|[freopnt]{3,4}bsd) (alpha)/i],[[y,/ower/,b,M]],[/winnt.+\[axp/i],[[y,"alpha"]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[C,[k,mi],[v,r]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr]|browser)[-\w]+)/i,/sec-(sgh\w+)/i],[C,[k,mi],[v,S]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[C,[k,a],[v,S]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[C,[k,a],[v,r]],[/(macintosh);/i],[C,[k,a]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[C,[k,"Sharp"],[v,S]],[/\b((?:brt|eln|hey2?|gdi|jdn)-a?[lnw]09|(?:ag[rm]3?|jdn2|kob2)-a?[lw]0[09]hn)(?: bui|\)|;)/i],[C,[k,bi],[v,r]],[/honor([-\w ]+)[;\)]/i],[C,[k,bi],[v,S]],[/\b((?:ag[rs][2356]?k?|bah[234]?|bg[2o]|bt[kv]|cmr|cpn|db[ry]2?|jdn2|got|kob2?k?|mon|pce|scm|sht?|[tw]gr|vrd)-[ad]?[lw][0125][09]b?|605hw|bg2-u03|(?:gem|fdr|m2|ple|t1)-[7a]0[1-4][lu]|t1-a2[13][lw]|mediapad[\w\. ]*(?= bui|\)))\b(?!.+d\/s)/i],[C,[k,wi],[v,r]],[/(?:huawei)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[C,[k,wi],[v,S]],[/oid[^\)]+; (2[\dbc]{4}(182|283|rp\w{2})[cgl]|m2105k81a?c)(?: bui|\))/i,/\b((?:red)?mi[-_ ]?pad[\w- ]*)(?: bui|\))/i],[[C,/_/g," "],[k,gi],[v,r]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i,/ ([\w ]+) miui\/v?\d/i],[[C,/_/g," "],[k,gi],[v,S]],[/droid.+; (cph2[3-6]\d[13579]|((gm|hd)19|(ac|be|in|kb)20|(d[en]|eb|le|mt)21|ne22)[0-2]\d|p[g-k]\w[1m]10)\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[C,[k,ui],[v,S]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[C,[k,pi],[v,S]],[/\b(opd2(\d{3}a?))(?: bui|\))/i],[C,[k,E,{OnePlus:["203","304","403","404","413","415"],"*":pi}],[v,r]],[/(vivo (5r?|6|8l?|go|one|s|x[il]?[2-4]?)[\w\+ ]*)(?: bui|\))/i],[C,[k,"BLU"],[v,S]],[/; vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[C,[k,"Vivo"],[v,S]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[C,[k,"Realme"],[v,S]],[/(ideatab[-\w ]+|602lv|d-42a|a101lv|a2109a|a3500-hv|s[56]000|pb-6505[my]|tb-?x?\d{3,4}(?:f[cu]|xu|[av])|yt\d?-[jx]?\d+[lfmx])( bui|;|\)|\/)/i,/lenovo ?(b[68]0[08]0-?[hf]?|tab(?:[\w- ]+?)|tb[\w-]{6,7})( bui|;|\)|\/)/i],[C,[k,di],[v,r]],[/lenovo[-_ ]?([-\w ]+?)(?: bui|\)|\/)/i],[C,[k,di],[v,S]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ]([\w\s]+)(\)| bui)/i,/((?:moto(?! 360)[-\w\(\) ]+|xt\d{3,4}[cgkosw\+]?[-\d]*|nexus 6)(?= bui|\)))/i],[C,[k,ci],[v,S]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[C,[k,ci],[v,r]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[C,[k,"LG"],[v,r]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+(?!.*(?:browser|netcast|android tv|watch|webos))(\w+)/i,/\blg-?([\d\w]+) bui/i],[C,[k,"LG"],[v,S]],[/(nokia) (t[12][01])/i],[k,C,[v,r]],[/(?:maemo|nokia).*(n900|lumia \d+|rm-\d+)/i,/nokia[-_ ]?(([-\w\. ]*))/i],[[C,/_/g," "],[v,S],[k,"Nokia"]],[/(pixel (c|tablet))\b/i],[C,[k,s],[v,r]],[/droid.+;(?: google)? (g(01[13]a|020[aem]|025[jn]|1b60|1f8f|2ybb|4s1m|576d|5nz6|8hhn|8vou|a02099|c15s|d1yq|e2ae|ec77|gh2x|kv4x|p4bc|pj41|r83y|tt9q|ur25|wvk6)|pixel[\d ]*a?( pro)?( xl)?( fold)?( \(5g\))?)( bui|\))/i],[C,[k,s],[v,S]],[/(google) (pixelbook( go)?)/i],[k,C],[/droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-\w\w\d\d)(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[C,[k,fi],[v,S]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[C,"Xperia Tablet"],[k,fi],[v,r]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[C,[k,ai],[v,r]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[C,/(.+)/g,"Fire Phone $1"],[k,ai],[v,S]],[/(playbook);[-\w\),; ]+(rim)/i],[C,k,[v,r]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[C,[k,ni],[v,S]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[C,[k,si],[v,r]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[C,[k,si],[v,S]],[/(nexus 9)/i],[C,[k,"HTC"],[v,r]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[k,[C,/_/g," "],[v,S]],[/tcl (xess p17aa)/i,/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])(_\w(\w|\w\w))?(\)| bui)/i],[C,[k,"TCL"],[v,r]],[/droid [\w\.]+; (418(?:7d|8v)|5087z|5102l|61(?:02[dh]|25[adfh]|27[ai]|56[dh]|59k|65[ah])|a509dl|t(?:43(?:0w|1[adepqu])|50(?:6d|7[adju])|6(?:09dl|10k|12b|71[efho]|76[hjk])|7(?:66[ahju]|67[hw]|7[045][bh]|71[hk]|73o|76[ho]|79w|81[hks]?|82h|90[bhsy]|99b)|810[hs]))(_\w(\w|\w\w))?(\)| bui)/i],[C,[k,"TCL"],[v,S]],[/(itel) ((\w+))/i],[[k,M],C,[v,E,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[C,[k,"Acer"],[v,r]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[C,[k,"Meizu"],[v,S]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[C,[k,"Ulefone"],[v,S]],[/; (energy ?\w+)(?: bui|\))/i,/; energizer ([\w ]+)(?: bui|\))/i],[C,[k,"Energizer"],[v,S]],[/; cat (b35);/i,/; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i],[C,[k,"Cat"],[v,S]],[/((?:new )?andromax[\w- ]+)(?: bui|\))/i],[C,[k,"Smartfren"],[v,S]],[/droid.+; (a(in)?(0(15|59|6[35])|142)p?)/i],[C,[k,"Nothing"],[v,S]],[/; (x67 5g|tikeasy \w+|ac[1789]\d\w+)( b|\))/i,/archos ?(5|gamepad2?|([\w ]*[t1789]|hello) ?\d+[\w ]*)( b|\))/i],[C,[k,"Archos"],[v,r]],[/archos ([\w ]+)( b|\))/i,/; (ac[3-6]\d\w{2,8})( b|\))/i],[C,[k,"Archos"],[v,S]],[/; (n159v)/i],[C,[k,"HMD"],[v,S]],[/(imo) (tab \w+)/i,/(infinix|tecno) (x1101b?|p904|dp(7c|8d|10a)( pro)?|p70[1-3]a?|p904|t1101)/i],[k,C,[v,r]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus(?! zenw)|dell|jolla|meizu|motorola|polytron|tecno|micromax|advan)[-_ ]?([-\w]*)/i,/; (blu|hmd|imo|infinix|lava|oneplus|tcl)[_ ]([\w\+ ]+?)(?: bui|\)|; r)/i,/(hp) ([\w ]+\w)/i,/(microsoft); (lumia[\w ]+)/i,/(oppo) ?([\w ]+) bui/i,/droid[^;]+; (philips)[_ ]([sv-x][\d]{3,4}[xz]?)/i],[k,C,[v,S]],[/(kobo)\s(ereader|touch)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i],[k,C,[v,r]],[/(surface duo)/i],[C,[k,li],[v,r]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[C,[k,"Fairphone"],[v,S]],[/((?:tegranote|shield t(?!.+d tv))[\w- ]*?)(?: b|\))/i],[C,[k,hi],[v,r]],[/(sprint) (\w+)/i],[k,C,[v,S]],[/(kin\.[onetw]{3})/i],[[C,/\./g," "],[k,li],[v,S]],[/droid.+; ([c6]+|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[C,[k,vi],[v,r]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[C,[k,vi],[v,S]],[/(philips)[\w ]+tv/i,/smart-tv.+(samsung)/i],[k,[v,i]],[/hbbtv.+maple;(\d+)/i],[[C,/^/,"SmartTV"],[k,mi],[v,i]],[/(vizio)(?: |.+model\/)(\w+-\w+)/i,/tcast.+(lg)e?. ([-\w]+)/i],[k,C,[v,i]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[k,"LG"],[v,i]],[/(apple) ?tv/i],[k,[C,a+" TV"],[v,i]],[/crkey.*devicetype\/chromecast/i],[[C,w+" Third Generation"],[k,s],[v,i]],[/crkey.*devicetype\/([^/]*)/i],[[C,/^/,"Chromecast "],[k,s],[v,i]],[/fuchsia.*crkey/i],[[C,w+" Nest Hub"],[k,s],[v,i]],[/crkey/i],[[C,w],[k,s],[v,i]],[/(portaltv)/i],[C,[k,Ci],[v,i]],[/droid.+aft(\w+)( bui|\))/i],[C,[k,ai],[v,i]],[/(shield \w+ tv)/i],[C,[k,hi],[v,i]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[C,[k,"Sharp"],[v,i]],[/(bravia[\w ]+)( bui|\))/i],[C,[k,fi],[v,i]],[/(mi(tv|box)-?\w+) bui/i],[C,[k,gi],[v,i]],[/Hbbtv.*(technisat) (.*);/i],[k,C,[v,i]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[k,/.+\/(\w+)/,"$1",E,{LG:"lge"}],[C,Mi],[v,i]],[/(playstation \w+)/i],[C,[k,fi],[v,W]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[C,[k,li],[v,W]],[/(ouya)/i,/(nintendo) (\w+)/i,/(retroid) (pocket ([^\)]+))/i],[k,C,[v,W]],[/droid.+; (shield)( bui|\))/i],[C,[k,hi],[v,W]],[/\b(sm-[lr]\d\d[0156][fnuw]?s?|gear live)\b/i],[C,[k,mi],[v,e]],[/((pebble))app/i,/(asus|google|lg|oppo) ((pixel |zen)?watch[\w ]*)( bui|\))/i],[k,C,[v,e]],[/(ow(?:19|20)?we?[1-3]{1,3})/i],[C,[k,pi],[v,e]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[C,[k,a],[v,e]],[/(opwwe\d{3})/i],[C,[k,ui],[v,e]],[/(moto 360)/i],[C,[k,ci],[v,e]],[/(smartwatch 3)/i],[C,[k,fi],[v,e]],[/(g watch r)/i],[C,[k,"LG"],[v,e]],[/droid.+; (wt63?0{2,3})\)/i],[C,[k,vi],[v,e]],[/droid.+; (glass) \d/i],[C,[k,s],[v,N]],[/(pico) ([\w ]+) os\d/i],[k,C,[v,N]],[/(quest( \d| pro)?s?).+vr/i],[C,[k,Ci],[v,N]],[/mobile vr; rv.+firefox/i],[[v,N]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[k,[v,F]],[/(aeobc)\b/i],[C,[k,ai],[v,F]],[/(homepod).+mac os/i],[C,[k,a],[v,F]],[/windows iot/i],[[v,F]],[/droid.+; ([\w- ]+) (4k|android|smart|google)[- ]?tv/i],[C,[v,i]],[/\b((4k|android|smart|opera)[- ]?tv|tv; rv:|large screen[\w ]+safari)\b/i],[[v,i]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+?(mobile|vr|\d) safari/i],[C,[v,E,{mobile:"Mobile",xr:"VR","*":r}]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[v,r]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[v,S]],[/droid .+?; ([\w\. -]+)( bui|\))/i],[C,[k,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[x,[g,xi+"HTML"]],[/(arkweb)\/([\w\.]+)/i],[g,x],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[x,[g,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[g,x],[/ladybird\//i],[[g,"LibWeb"]],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[x,g]],os:[[/(windows nt) (6\.[23]); arm/i],[[g,/N/,"R"],[x,E,ji]],[/(windows (?:phone|mobile|iot))(?: os)?[\/ ]?([\d\.]*( se)?)/i,/(windows)[\/ ](1[01]|2000|3\.1|7|8(\.1)?|9[58]|me|server 20\d\d( r2)?|vista|xp)/i],[g,x],[/windows nt ?([\d\.\)]*)(?!.+xbox)/i,/\bwin(?=3| ?9|n)(?:nt| 9x )?([\d\.;]*)/i],[[x,/(;|\))/g,"",E,ji],[g,_i]],[/(windows ce)\/?([\d\.]*)/i],[g,x],[/[adehimnop]{4,7}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[x,/_/g,"."],[g,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+(haiku|morphos))/i],[[g,"macOS"],[x,/_/g,"."]],[/android ([\d\.]+).*crkey/i],[x,[g,w+" Android"]],[/fuchsia.*crkey\/([\d\.]+)/i],[x,[g,w+" Fuchsia"]],[/crkey\/([\d\.]+).*devicetype\/smartspeaker/i],[x,[g,w+" SmartSpeaker"]],[/linux.*crkey\/([\d\.]+)/i],[x,[g,w+" Linux"]],[/crkey\/([\d\.]+)/i],[x,[g,w]],[/droid ([\w\.]+)\b.+(android[- ]x86)/i],[x,g],[/(ubuntu) ([\w\.]+) like android/i],[[g,/(.+)/,"$1 Touch"],x],[/(harmonyos)[\/ ]?([\d\.]*)/i,/(android|bada|blackberry|kaios|maemo|meego|openharmony|qnx|rim tablet os|sailfish|series40|symbian|tizen)\w*[-\/\.; ]?([\d\.]*)/i],[g,x],[/\(bb(10);/i],[x,[g,ni]],[/(?:symbian ?os|symbos|s60(?=;)|series ?60)[-\/ ]?([\w\.]*)/i],[x,[g,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[x,[g,yi+" OS"]],[/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i,/webos(?:[ \/]?|\.tv-20(?=2[2-9]))(\d[\d\.]*)/i],[x,[g,"webOS"]],[/web0s;.+?(?:chr[o0]me|safari)\/(\d+)/i],[[x,E,{25:"120",24:"108",23:"94",22:"87",6:"79",5:"68",4:"53",3:"38",2:"538",1:"537","*":"TV"}],[g,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[x,[g,"watchOS"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[g,"Chrome OS"],x],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) (\w+)/i,/(xbox); +xbox ([^\);]+)/i,/(pico) .+os([\w\.]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/linux.+(mint)[\/\(\) ]?([\w\.]*)/i,/(mageia|vectorlinux|fuchsia|arcaos|arch(?= ?linux))[;l ]([\d\.]*)/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire|knoppix)(?: gnu[\/ ]linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/\b(aix)[; ]([1-9\.]{0,4})/i,/(hurd|linux|morphos)(?: (?:arm|x86|ppc)\w*| ?)([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) ?(r\d)?/i],[g,x],[/(sunos) ?([\d\.]*)/i],[[g,"Solaris"],x],[/\b(beos|os\/2|amigaos|openvms|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[g,x]]},Pi=(d={init:{},isIgnore:{},isIgnoreRgx:{},toString:{}},A.call(d.init,[[c,[g,x,G,v]],[h,[y]],[u,[v,C,k]],[p,[g,x]],[m,[g,x]]]),A.call(d.isIgnore,[[c,[x,G]],[p,[x]],[m,[x]]]),A.call(d.isIgnoreRgx,[[c,/ ?browser$/i],[m,/ ?os$/i]]),A.call(d.toString,[[c,[g,x]],[h,[y]],[u,[k,C]],[p,[g,x]],[m,[g,x]]]),d),Bi=function(e,i){var o=Pi.init[i],t=Pi.isIgnore[i]||0,r=Pi.isIgnoreRgx[i]||0,a=Pi.toString[i]||0;function s(){A.call(this,o)}return s.prototype.getItem=function(){return e},s.prototype.withClientHints=function(){return T?T.getHighEntropyValues(ri).then(function(i){return e.setCH(new Ui(i,!1)).parseCH().get()}):e.parseCH().get()},s.prototype.withFeatureCheck=function(){return e.detectFeature().get()},i!=f&&(s.prototype.is=function(i){var e,o=!1;for(e in this)if(this.hasOwnProperty(e)&&!qi(t,e)&&M(r?j(r,this[e]):this[e])==M(r?j(r,i):i)){if(o=!0,i!=n)break}else if(i==n&&o){o=!o;break}return o},s.prototype.toString=function(){var i,e=b;for(i in a)typeof this[a[i]]!==n&&(e+=(e?" ":b)+this[a[i]]);return e||n}),T||(s.prototype.then=function(i){function e(){for(var i in o)o.hasOwnProperty(i)&&(this[i]=o[i])}var o=this,t=(e.prototype={is:s.prototype.is,toString:s.prototype.toString},new e);return i(t),t}),new s};function Ui(i,e){if(i=i||{},A.call(this,ri),e)A.call(this,[[D,Oi(i[t])],[$,Oi(i[Z])],[S,/\?1/.test(i[ii])],[C,Hi(i[ei])],[q,Hi(i[oi])],[X,Hi(i[ti])],[y,Hi(i[K])],[_,Oi(i[J])],[Y,Hi(i[Q])]]);else for(var o in i)this.hasOwnProperty(o)&&typeof i[o]!==n&&(this[o]=i[o])}function Li(i,e,o,t){return this.get=function(i){return i?this.data.hasOwnProperty(i)?this.data[i]:void 0:this.data},this.set=function(i,e){return this.data[i]=e,this},this.setCH=function(i){return this.uaCH=i,this},this.detectFeature=function(){if(O&&O.userAgent==this.ua)switch(this.itemType){case c:O.brave&&typeof O.brave.isBrave==L&&this.set(g,"Brave");break;case u:!this.get(v)&&T&&T[S]&&this.set(v,S),"Macintosh"==this.get(C)&&O&&typeof O.standalone!==n&&O.maxTouchPoints&&2<O.maxTouchPoints&&this.set(C,"iPad").set(v,r);break;case m:!this.get(g)&&T&&T[q]&&this.set(g,T[q]);break;case f:var e=this.data,i=function(i){return e[i].getItem().detectFeature().get()};this.set(c,i(c)).set(h,i(h)).set(u,i(u)).set(p,i(p)).set(m,i(m))}return this},this.parseUA=function(){return this.itemType!=f&&Ai.call(this.data,this.ua,this.rgxMap),this.itemType==c&&this.set(G,Ti(this.get(x))),this},this.parseCH=function(){var i,e=this.uaCH,o=this.rgxMap;switch(this.itemType){case c:case p:var t,r=e[$]||e[D];if(r)for(var a=0;a<r.length;a++){var s=r[a].brand||r[a],n=r[a].version;this.itemType==c&&!/not.a.brand/i.test(s)&&(!t||/Chrom/.test(t)&&s!=ki||t==xi&&/WebView2/.test(s))&&(s=E(s,Vi),(t=this.get(g))&&!/Chrom/.test(t)&&/Chrom/.test(s)||this.set(g,s).set(x,n).set(G,Ti(n)),t=s),this.itemType==p&&s==ki&&this.set(x,n)}break;case h:var w=e[y];w&&("64"==e[Y]&&(w+="64"),Ai.call(this.data,w+";",o));break;case u:if(e[S]&&this.set(v,S),e[C]&&(this.set(C,e[C]),this.get(v)&&this.get(k)||(Ai.call(w={},"droid 9; "+e[C]+")",o),!this.get(v)&&w.type&&this.set(v,w.type),!this.get(k)&&w.vendor&&this.set(k,w.vendor))),e[_]){if("string"!=typeof e[_])for(var d=0;!i&&d<e[_].length;)i=E(e[_][d++],Ei);else i=E(e[_],Ei);this.set(v,i)}break;case m:var b,w=e[q];w&&(b=e[X],w==_i&&(b=13<=parseInt(Ti(b),10)?"11":"10"),this.set(g,w).set(x,b)),this.get(g)==_i&&"Xbox"==e[C]&&this.set(g,"Xbox").set(x,void 0);break;case f:var l=this.data,w=function(i){return l[i].getItem().setCH(e).parseCH().get()};this.set(c,w(c)).set(h,w(h)).set(u,w(u)).set(p,w(p)).set(m,w(m))}return this},A.call(this,[["itemType",i],["ua",e],["uaCH",t],["rgxMap",o],["data",Bi(this,i)]]),this}function V(i,e,o){if(typeof i===l?(e=zi(i,!0)?(typeof e===l&&(o=e),i):void(o=i),i=void 0):typeof i!==R||zi(e,!0)||(o=e,e=void 0),o)if(typeof o.append===L){var t={};o.forEach(function(i,e){t[String(e).toLowerCase()]=i}),o=t}else{var r,a={};for(r in o)o.hasOwnProperty(r)&&(a[String(r).toLowerCase()]=o[r]);o=a}var s,n,w,d;return this instanceof V?(s=typeof i===R?i:o&&o[B]?o[B]:O&&O.userAgent?O.userAgent:b,n=new Ui(o,!0),w=e?((i,e)=>{var o,t={},r=e;if(!zi(e))for(var a in r={},e)for(var s in e[a])r[s]=e[a][s].concat(r[s]||[]);for(o in i)t[o]=r[o]&&r[o].length%2==0?r[o].concat(i[o]):i[o];return t})(Ii,e):Ii,A.call(this,[["getBrowser",(d=function(i){return i==f?function(){return new Li(i,s,w,n).set("ua",s).set(c,this.getBrowser()).set(h,this.getCPU()).set(u,this.getDevice()).set(p,this.getEngine()).set(m,this.getOS()).get()}:function(){return new Li(i,s,w[i],n).parseUA().get()}})(c)],["getCPU",d(h)],["getDevice",d(u)],["getEngine",d(p)],["getOS",d(m)],["getResult",d(f)],["getUA",function(){return s}],["setUA",function(i){return H(i)&&(s=i.length>P?Mi(i,P):i),this}]]).setUA(s),this):new V(i,e,o).getResult()}V.VERSION="2.0.5",V.BROWSER=I([g,x,G,v]),V.CPU=I([y]),V.DEVICE=I([C,k,v,W,S,i,r,e,F]),V.ENGINE=V.OS=I([g,x]);export{V as UAParser};