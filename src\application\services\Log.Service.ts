// ---------- FILE PATH
// src/application/services/log.service.ts

// ---------- DESCRIPTION
// This service handles the business logic for log entries, including creation and retrieval.

// Import the required interfaces.
import { ILogService } from "../../application/interfaces/IServices/ILog.Service.js";
import { ILogRepository } from "../../application/interfaces/IRepositories/ILog.Repositories.js"
// Import the log entity.
import { Log } from "../../domain/Log.entity.js";

// Import the UAParser library.
import { UAParser } from "ua-parser-js";

export class LogService implements ILogService {
  // Constructor
  constructor(private readonly logs: ILogRepository) {}

  // Create a new log entry.
  async createLog(
    description: string,
    ipAddress: string,
    actionType: string,
    deviceInfo?: string,
    browserInfo?: string
  ): Promise<Log> {
    const log = new Log(
      undefined,
      description,
      ipAddress,
      new Date(),
      deviceInfo,
      browserInfo,
      actionType
    );
    return this.logs.create(log);
  }

  // Create a new log entry from a request.
  async createLogFromRequest(req: any, description: string): Promise<Log> {
    // Extract IP & User-Agent from request
    const uaString = req.headers["user-agent"] || "unknown";
    const ip = (req.ip || req.socket?.remoteAddress || "unknown").toString();
    const actionType = req.method;

    // Parse User-Agent using centralized logic
    const { deviceInfo, browserInfo } = this.parseUserAgent(uaString);

    return this.createLog(description, ip, actionType, deviceInfo, browserInfo);
  }

  // Private method to handle all UA parsing logic
  private parseUserAgent(uaString: string): {
    deviceInfo: string;
    browserInfo: string;
  } {
    const parser = new UAParser(uaString);

    const deviceInfo = parser.getDevice(); // { vendor, model, type }
    const osInfo = parser.getOS(); // { name, version }
    const browserInfo = parser.getBrowser(); // { name, version }

    const isPostman = uaString.toLowerCase().includes("postmanruntime");
    const isOtherApiClient =
      uaString.toLowerCase().includes("insomnia") ||
      uaString.toLowerCase().includes("curl") ||
      uaString.toLowerCase().includes("httpie");

    const isApiClient =
      isPostman ||
      isOtherApiClient ||
      (!deviceInfo.vendor &&
        !deviceInfo.model &&
        !osInfo.name &&
        !browserInfo.name);

    let deviceSummary: string;
    let osSummary: string;
    let browserSummary: string;

    if (isPostman) {
      deviceSummary = "API Testing Tool";
      osSummary = "Development Environment";
      browserSummary = "Postman";
    } else if (isOtherApiClient) {
      deviceSummary = "API Client";
      osSummary = "Development Environment";
      browserSummary = uaString.toLowerCase().includes("insomnia")
        ? "Insomnia"
        : uaString.toLowerCase().includes("curl")
        ? "cURL"
        : uaString.toLowerCase().includes("httpie")
        ? "HTTPie"
        : "API Client";
    } else if (isApiClient) {
      deviceSummary = "Unknown API Client";
      osSummary = "Unknown Environment";
      browserSummary = browserInfo.name
        ? `${browserInfo.name} ${browserInfo.version || ""}`.trim()
        : "Unknown Client";
    } else {
      deviceSummary =
        deviceInfo.vendor || deviceInfo.model
          ? `${deviceInfo.vendor || ""} ${deviceInfo.model || ""} (${
              deviceInfo.type || "desktop"
            })`.trim()
          : `Desktop (${osInfo.name || "Unknown OS"} ${osInfo.version || ""})`;

      osSummary = `${osInfo.name || "Unknown OS"} ${
        osInfo.version || ""
      }`.trim();
      browserSummary = `${browserInfo.name || "Unknown Browser"} ${
        browserInfo.version || ""
      }`.trim();
    }

    return {
      deviceInfo: `${deviceSummary} on ${osSummary}`,
      browserInfo: browserSummary,
    };
  }
}