{"name": "ptei_backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "seed": "tsx src/scripts/seed.ts", "seed:force": "SEED_ON_START=true npm run dev", "check-admin": "tsx src/scripts/check-admin.ts", "fix-student-usertype": "tsx src/scripts/fix-student-usertype.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@fastify/cors": "^11.1.0", "@fastify/multipart": "^9.0.3", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.3", "@prisma/client": "^6.15.0", "@types/jsonwebtoken": "^9.0.10", "ajv": "^8.12.0", "ajv-formats": "^2.1.1", "dotenv": "^17.2.1", "fastify": "^5.5.0", "fastify-plugin": "^5.0.1", "jsonwebtoken": "^9.0.2", "mssql": "^11.0.1", "nodemailer": "^6.9.8", "ua-parser-js": "^2.0.5", "zod": "^4.1.5"}, "devDependencies": {"@types/node": "^24.3.0", "@types/nodemailer": "^6.4.14", "pino-pretty": "^13.1.1", "prisma": "^6.14.0", "tsx": "^4.20.4", "typescript": "^5.9.2"}}