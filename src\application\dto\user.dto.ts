// User DTOs - Using Zod for validation
import { z } from "zod";

// Base User DTO Schema for responses
export const UserSchema = z.object({
  userId: z.uuid(),
  email: z.email(),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  roleId: z.uuid(),
  roleName: z.string(),
  departmentId: z.uuid().optional(),
  departmentName: z.string(),
  userType: z.string(),
  accountStatus: z.string(),
  userCreatedAt: z.date(),
  userModifiedAt: z.date(),
  isAccountLockedOut: z.boolean(),
  failedLoginAttempts: z.number(),
  userProfilePicPath: z.string(),
});

export type UserDto = z.infer<typeof UserSchema>;

// Create User DTO Schema
export const CreateUserSchema = z.object({
  email: z.email().min(1, "Email is required"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  departmentId: z.uuid().nullable().optional(),
  roleId: z.uuid(),
  userType: z.enum(["TSLS", "University"]),
  universityId: z.uuid().optional(),
});

export type CreateUserDto = z.infer<typeof CreateUserSchema>;

// User Registration DTO Schema - Complete registration form
export const UserRegistrationSchema = z.object({
  // Basic Information
  tin: z.string().min(1, "TIN is required").max(50, "TIN too long"),
  firstName: z.string().min(1, "First name is required").max(100, "First name too long"),
  otherName: z.string().max(100, "Other name too long").optional(),
  lastName: z.string().min(1, "Last name is required").max(100, "Last name too long"),
  email: z.string().email().min(1, "Email is required"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  phoneContact: z.string().min(1, "Phone contact is required").max(20, "Phone contact too long"),
  title: z.string().min(1, "Title is required").max(10, "Title too long"),
  gender: z.string().min(1, "Gender is required").max(10, "Gender too long"),
  ethnicity: z.string().min(1, "Ethnicity is required").max(100, "Ethnicity too long"),
  dateOfBirth: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),

  // Address Information
  residentialAddress: z.string().min(1, "Residential address is required").max(500, "Address too long"),
  postalAddress: z.string().max(500, "Postal address too long").optional(),
  province: z.string().min(1, "Province is required").max(100, "Province too long"),

  // TSLS Information
  tslsScheme: z.string().min(1, "TSLS scheme is required").max(200, "TSLS scheme too long"),
  roleId: z.uuid(),

  // Profile Picture (optional - will be handled as file upload)
  profilePicture: z.string().max(1000000, "Profile picture too large").optional(),
});

export type UserRegistrationDto = z.infer<typeof UserRegistrationSchema>;

// Update User DTO Schema
export const UpdateUserSchema = z.object({
  userId: z.uuid(),
  firstName: z.string().min(1, "First name is required").optional(),
  lastName: z.string().min(1, "Last name is required").optional(),
  departmentId: z.uuid().optional(),
  roleId: z.uuid().optional(),
});

export type UpdateUserDto = z.infer<typeof UpdateUserSchema>;

// Change Status DTO Schema
export const ChangeStatusSchema = z.object({
  userId: z.uuid(),
  newStatus: z.string().min(1, "New status is required"),
});

export type ChangeStatusDto = z.infer<typeof ChangeStatusSchema>;

// Unlock Account DTO Schema
export const UnlockAccountSchema = z.object({
  userId: z.uuid(),
});

export type UnlockAccountDto = z.infer<typeof UnlockAccountSchema>;

// Reset Password DTO Schema
export const ResetPasswordSchema = z.object({
  email: z.email(),
  token: z.string().min(1, "Token is required"),
  newPassword: z.string().min(8, "Password must be at least 8 characters"),
});

export type ResetPasswordDto = z.infer<typeof ResetPasswordSchema>;

// Verify Email DTO Schema
export const VerifyEmailSchema = z.object({
  email: z.email(),
  token: z.string().min(1, "Token is required"),
});

export type VerifyEmailDto = z.infer<typeof VerifyEmailSchema>;

// Create University DTO Schema (moved from university.dto.ts for consistency)
export const CreateUniversitySchema = z.object({
  universityName: z.string().min(1, "University name is required"),
  primaryEmail: z.email(),
  ccEmails: z.string().optional(),
  bccEmails: z.string().optional(),
  contactPerson: z.string().min(1, "Contact person is required"),
  phone: z.string().min(1, "Phone is required"),
});

export type CreateUniversityDto = z.infer<typeof CreateUniversitySchema>;

// Update University DTO Schema (moved from university.dto.ts for consistency)
export const UpdateUniversitySchema = z.object({
  universityId: z.uuid(),
  universityName: z.string().min(1, "University name is required").optional(),
  primaryEmail: z.email().optional(),
  ccEmails: z.string().optional(),
  bccEmails: z.string().optional(),
  contactPerson: z.string().min(1, "Contact person is required").optional(),
  phone: z.string().min(1, "Phone is required").optional(),
});

export type UpdateUniversityDto = z.infer<typeof UpdateUniversitySchema>;

// Export schemas with DtoSchema suffix for backward compatibility
export const UnlockAccountDtoSchema = UnlockAccountSchema;
