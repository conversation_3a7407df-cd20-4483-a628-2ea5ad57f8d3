generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "sqlserver"
  url      = env("DATABASE_URL")
}

model PTEIUser {
  UserId                       String    @id @default(uuid()) @db.UniqueIdentifier
  email                        String    @unique @db.NVarChar(1000)
  HashedPassword               String    @db.NVarChar(1000)
  FirstName                    String    @default("") @db.NVarChar(1000)
  LastName                     String    @default("") @db.NVarChar(1000)
  UserProfilePicPath           String    @default("") @db.NVarChar(1000)
  UserType                     String?   @db.NVarChar(1000)
  AccountStatus                String    @default("") @db.NVarChar(1000)
  IsAccountLockedOut           Boolean   @default(false) @db.Bit
  FailedLoginAttempts          Int       @default(0)
  IsUserLoggedIn               Boolean   @default(false) @db.Bit
  UserCreatedAt                DateTime  @default(now()) @db.DateTimeOffset
  UserModifiedAt               DateTime? @db.DateTimeOffset
  EmailVerificationToken       String?   @default("") @db.NVarChar(1000)
  EmailVerificationTokenExpiry DateTime? @db.DateTimeOffset
  PasswordResetToken           String?   @default("") @db.NVarChar(1000)
  PasswordResetTokenExpiry     DateTime? @db.DateTimeOffset
  RoleId                       String?   @db.UniqueIdentifier
  DepartmentId                 String?   @db.UniqueIdentifier

  // New registration fields
  TIN                String?   @db.NVarChar(50)
  OtherName          String?   @db.NVarChar(1000)
  PhoneContact       String?   @db.NVarChar(20)
  Title              String?   @db.NVarChar(20)
  Gender             String?   @db.NVarChar(50)
  Ethnicity          String?   @db.NVarChar(100)
  DateOfBirth        DateTime? @db.Date
  ResidentialAddress String?   @db.NVarChar(500)
  PostalAddress      String?   @db.NVarChar(500)
  Province           String?   @db.NVarChar(100)
  TSLSScheme         String?   @db.NVarChar(200)

  // Relations
  Role            Role?            @relation(fields: [RoleId], references: [RoleId])
  Department      Department?      @relation(fields: [DepartmentId], references: [DepartmentId])
  Log             Log[]
  UniversityUsers UniversityUser[]
  ReportSettings  ReportSettings[]

  @@map("PTEIUser")
}

model Role {
  RoleId      String   @id @default(uuid()) @db.UniqueIdentifier
  RoleName    String   @db.NVarChar(1000)
  Description String?  @db.NVarChar(1000)
  IsActive    Boolean  @default(true) @db.Bit
  CreatedAt   DateTime @default(now()) @db.DateTimeOffset

  // Relations
  Users           PTEIUser[]
  RolePermissions RolePermission[]

  @@map("Role")
}

model Department {
  DepartmentId   String   @id @default(uuid()) @db.UniqueIdentifier
  DepartmentName String   @db.NVarChar(1000)
  Description    String?  @db.NVarChar(1000)
  IsActive       Boolean  @default(true) @db.Bit
  CreatedAt      DateTime @default(now()) @db.DateTimeOffset

  // Relations
  Users PTEIUser[]

  @@map("Department")
}

model Permission {
  PermissionId String  @id @default(uuid()) @db.UniqueIdentifier
  Name         String  @db.NVarChar(1000)
  Description  String? @db.NVarChar(1000)

  // Relations
  RolePermissions RolePermission[]

  @@map("Permission")
}

model RolePermission {
  RoleId       String @db.UniqueIdentifier
  PermissionId String @db.UniqueIdentifier

  // Relations
  Role       Role       @relation(fields: [RoleId], references: [RoleId])
  Permission Permission @relation(fields: [PermissionId], references: [PermissionId])

  @@id([RoleId, PermissionId])
  @@map("RolePermission")
}

model University {
  UniversityId   String   @id @default(uuid()) @db.UniqueIdentifier
  UniversityName String   @db.NVarChar(1000)
  PrimaryEmail   String   @db.NVarChar(1000)
  CCEmails       String?  @db.NVarChar(1000)
  BCCEmails      String?  @db.NVarChar(1000)
  ContactPerson  String   @db.NVarChar(1000)
  Phone          String   @db.NVarChar(1000)
  IsActive       Boolean  @default(true) @db.Bit
  CreatedAt      DateTime @default(now()) @db.DateTimeOffset
  UpdatedAt      DateTime @updatedAt @db.DateTimeOffset

  // Relations
  UniversityUsers    UniversityUser[]
  TslsToUniDropboxes TslsToUniDropbox[]
  UniToTslsDropboxes UniToTslsDropbox[]

  @@map("University")
}

model UniversityUser {
  UniversityUserId String   @id @default(uuid()) @db.UniqueIdentifier
  CreatedAt        DateTime @default(now()) @db.DateTimeOffset
  IsActive         Boolean  @default(true) @db.Bit
  UserId           String   @db.UniqueIdentifier
  UniversityId     String   @db.UniqueIdentifier

  // Relations
  User       PTEIUser   @relation(fields: [UserId], references: [UserId])
  University University @relation(fields: [UniversityId], references: [UniversityId])

  @@map("UniversityUser")
}

model Log {
  id             String    @id @default(uuid())
  description    String
  ipAddress      String
  timeStamp      DateTime  @default(now())
  deviceInfo     String?
  browserInfo    String?
  actionType     String
  PTEIUser       PTEIUser? @relation(fields: [pTEIUserUserId], references: [UserId])
  pTEIUserUserId String?   @db.UniqueIdentifier
}

model TslsToUniDropbox {
  TslsToUniDropboxId   String    @id @default(uuid()) @db.UniqueIdentifier
  TslsToUniDropboxName String    @db.NVarChar(1000)
  UniversityId         String    @db.UniqueIdentifier
  Year                 Int
  ModeOfStudy          String    @db.NVarChar(1000)
  Term                 Int
  BatchNumber          Int
  CreatedAt            DateTime  @default(now()) @db.DateTimeOffset
  UpdatedAt            DateTime? @db.DateTimeOffset
  OpeningDate          DateTime  @db.DateTimeOffset
  ClosingDate          DateTime  @db.DateTimeOffset
  IsOpenStatus         Boolean   @db.Bit
  ReportType           String    @db.NVarChar(100) // Add this field

  // Relations
  University                 University                  @relation(fields: [UniversityId], references: [UniversityId])
  TslsToUniReportSubmissions TslsToUniReportSubmission[]

  @@map("TslsToUniDropbox")
}

model TslsToUniReportSubmission {
  TslsToUniReportSubmissionId       String   @id @default(uuid()) @db.UniqueIdentifier
  TslsToUniReportSubmissionName     String   @db.NVarChar(1000)
  TslsToUniReportSubmissionFilePath String   @db.NVarChar(1000)
  SubmittedAt                       DateTime @default(now()) @db.DateTimeOffset
  Notes                             String   @default("") @db.NVarChar(1000)
  SubmittedBy                       String   @db.NVarChar(1000)
  TslsToUniDropboxId                String   @db.UniqueIdentifier
  BatchNumber                       String?  @db.NVarChar(100) // Add this field
  Status                            String   @default("Pending") @db.NVarChar(50) // Add this field

  // Relations
  TslsToUniDropbox TslsToUniDropbox @relation(fields: [TslsToUniDropboxId], references: [TslsToUniDropboxId])

  @@map("TslsToUniReportSubmission")
}

model UniToTslsDropbox {
  UniToTslsDropboxId   String    @id @default(uuid()) @db.UniqueIdentifier
  UniToTslsDropboxName String    @db.NVarChar(1000)
  CreatedAt            DateTime  @default(now()) @db.DateTimeOffset
  UpdatedAt            DateTime? @db.DateTimeOffset
  OpeningDate          DateTime  @db.DateTimeOffset
  ClosingDate          DateTime  @db.DateTimeOffset
  IsOpen               Boolean   @db.Bit
  ReportType           String    @db.NVarChar(100) // Add this field
  UniversityId         String    @db.UniqueIdentifier // Add this field

  // Relations
  University                 University                  @relation(fields: [UniversityId], references: [UniversityId])
  UniToTslsReportSubmissions UniToTslsReportSubmission[]

  @@map("UniToTslsDropbox")
}

model UniToTslsReportSubmission {
  UniToTslsReportSubmissionId       String   @id @default(uuid()) @db.UniqueIdentifier
  UniToTslsReportSubmissionName     String   @db.NVarChar(1000)
  UniToTslsReportSubmissionFilePath String   @db.NVarChar(1000)
  SubmittedAt                       DateTime @default(now()) @db.DateTimeOffset
  Notes                             String   @default("") @db.NVarChar(1000)
  SubmittedBy                       String   @db.NVarChar(1000)
  UniToTslsDropboxId                String   @db.UniqueIdentifier
  BatchNumber                       String?  @db.NVarChar(100) // Add this field
  Status                            String   @default("Pending") @db.NVarChar(50) // Add this field

  // Relations
  UniToTslsDropbox UniToTslsDropbox @relation(fields: [UniToTslsDropboxId], references: [UniToTslsDropboxId])

  @@map("UniToTslsReportSubmission")
}

// Add this new model for Report Settings
model ReportSettings {
  ReportSettingsId  String   @id @default(uuid()) @db.UniqueIdentifier
  ReportType        String   @db.NVarChar(100)
  ReportOpeningDate DateTime @db.DateTimeOffset
  ReportClosingDate DateTime @db.DateTimeOffset
  IsActive          Boolean  @default(true) @db.Bit
  CreatedBy         String   @db.UniqueIdentifier
  CreatedAt         DateTime @default(now()) @db.DateTimeOffset
  UpdatedAt         DateTime @updatedAt @db.DateTimeOffset

  // Relations
  Creator PTEIUser @relation(fields: [CreatedBy], references: [UserId])

  @@unique([ReportType])
  @@map("ReportSettings")
}

// Add this new model for Email Notifications
model EmailNotification {
  EmailNotificationId String   @id @default(uuid()) @db.UniqueIdentifier
  RecipientEmail      String   @db.NVarChar(1000)
  Subject             String   @db.NVarChar(1000)
  Body                String   @db.NVarChar(4000)
  SentAt              DateTime @default(now()) @db.DateTimeOffset
  Status              String   @db.NVarChar(50)
  NotificationType    String   @db.NVarChar(100)
  RelatedEntityId     String?  @db.UniqueIdentifier
  RelatedEntityType   String?  @db.NVarChar(100)

  @@map("EmailNotification")
}

// Add this new model for Email Templates
model EmailTemplate {
  EmailTemplateId String   @id @default(uuid()) @db.UniqueIdentifier
  TemplateName    String   @db.NVarChar(200)
  TemplateSubject String   @db.NVarChar(500)
  TemplateBody    String   @db.NVarChar(4000)
  TemplateType    String   @db.NVarChar(100)
  IsActive        Boolean  @default(true)
  CreatedAt       DateTime @default(now()) @db.DateTimeOffset
  UpdatedAt       DateTime @updatedAt @db.DateTimeOffset

  @@map("EmailTemplate")
}
