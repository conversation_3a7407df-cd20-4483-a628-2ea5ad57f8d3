
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
  PrismaClientRustPanicError,
  PrismaClientInitializationError,
  PrismaClientValidationError,
  getPrismaClient,
  sqltag,
  empty,
  join,
  raw,
  skip,
  Decimal,
  Debug,
  objectEnumValues,
  makeStrictEnum,
  Extensions,
  warnOnce,
  defineDmmfProperty,
  Public,
  getRuntime,
  createParam,
} = require('./runtime/library.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.14.0
 * Query Engine version: 717184b7b35ea05dfa71a3236b7af656013e1e49
 */
Prisma.prismaVersion = {
  client: "6.14.0",
  engine: "717184b7b35ea05dfa71a3236b7af656013e1e49"
}

Prisma.PrismaClientKnownRequestError = PrismaClientKnownRequestError;
Prisma.PrismaClientUnknownRequestError = PrismaClientUnknownRequestError
Prisma.PrismaClientRustPanicError = PrismaClientRustPanicError
Prisma.PrismaClientInitializationError = PrismaClientInitializationError
Prisma.PrismaClientValidationError = PrismaClientValidationError
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = sqltag
Prisma.empty = empty
Prisma.join = join
Prisma.raw = raw
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = Extensions.getExtensionContext
Prisma.defineExtension = Extensions.defineExtension

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}




  const path = require('path')

/**
 * Enums
 */
exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable',
  Snapshot: 'Snapshot'
});

exports.Prisma.PTEIUserScalarFieldEnum = {
  UserId: 'UserId',
  email: 'email',
  HashedPassword: 'HashedPassword',
  FirstName: 'FirstName',
  LastName: 'LastName',
  UserProfilePicPath: 'UserProfilePicPath',
  UserType: 'UserType',
  AccountStatus: 'AccountStatus',
  IsAccountLockedOut: 'IsAccountLockedOut',
  FailedLoginAttempts: 'FailedLoginAttempts',
  IsUserLoggedIn: 'IsUserLoggedIn',
  UserCreatedAt: 'UserCreatedAt',
  UserModifiedAt: 'UserModifiedAt',
  EmailVerificationToken: 'EmailVerificationToken',
  EmailVerificationTokenExpiry: 'EmailVerificationTokenExpiry',
  PasswordResetToken: 'PasswordResetToken',
  PasswordResetTokenExpiry: 'PasswordResetTokenExpiry',
  RoleId: 'RoleId',
  DepartmentId: 'DepartmentId',
  TIN: 'TIN',
  OtherName: 'OtherName',
  PhoneContact: 'PhoneContact',
  Title: 'Title',
  Gender: 'Gender',
  Ethnicity: 'Ethnicity',
  DateOfBirth: 'DateOfBirth',
  ResidentialAddress: 'ResidentialAddress',
  PostalAddress: 'PostalAddress',
  Province: 'Province',
  TSLSScheme: 'TSLSScheme'
};

exports.Prisma.RoleScalarFieldEnum = {
  RoleId: 'RoleId',
  RoleName: 'RoleName',
  Description: 'Description',
  IsActive: 'IsActive',
  CreatedAt: 'CreatedAt'
};

exports.Prisma.DepartmentScalarFieldEnum = {
  DepartmentId: 'DepartmentId',
  DepartmentName: 'DepartmentName',
  Description: 'Description',
  IsActive: 'IsActive',
  CreatedAt: 'CreatedAt'
};

exports.Prisma.PermissionScalarFieldEnum = {
  PermissionId: 'PermissionId',
  Name: 'Name',
  Description: 'Description'
};

exports.Prisma.RolePermissionScalarFieldEnum = {
  RoleId: 'RoleId',
  PermissionId: 'PermissionId'
};

exports.Prisma.UniversityScalarFieldEnum = {
  UniversityId: 'UniversityId',
  UniversityName: 'UniversityName',
  PrimaryEmail: 'PrimaryEmail',
  CCEmails: 'CCEmails',
  BCCEmails: 'BCCEmails',
  ContactPerson: 'ContactPerson',
  Phone: 'Phone',
  IsActive: 'IsActive',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt'
};

exports.Prisma.UniversityUserScalarFieldEnum = {
  UniversityUserId: 'UniversityUserId',
  CreatedAt: 'CreatedAt',
  IsActive: 'IsActive',
  UserId: 'UserId',
  UniversityId: 'UniversityId'
};

exports.Prisma.LogScalarFieldEnum = {
  id: 'id',
  description: 'description',
  ipAddress: 'ipAddress',
  timeStamp: 'timeStamp',
  deviceInfo: 'deviceInfo',
  browserInfo: 'browserInfo',
  actionType: 'actionType',
  pTEIUserUserId: 'pTEIUserUserId'
};

exports.Prisma.TslsToUniDropboxScalarFieldEnum = {
  TslsToUniDropboxId: 'TslsToUniDropboxId',
  TslsToUniDropboxName: 'TslsToUniDropboxName',
  UniversityId: 'UniversityId',
  Year: 'Year',
  ModeOfStudy: 'ModeOfStudy',
  Term: 'Term',
  BatchNumber: 'BatchNumber',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt',
  OpeningDate: 'OpeningDate',
  ClosingDate: 'ClosingDate',
  IsOpenStatus: 'IsOpenStatus',
  ReportType: 'ReportType'
};

exports.Prisma.TslsToUniReportSubmissionScalarFieldEnum = {
  TslsToUniReportSubmissionId: 'TslsToUniReportSubmissionId',
  TslsToUniReportSubmissionName: 'TslsToUniReportSubmissionName',
  TslsToUniReportSubmissionFilePath: 'TslsToUniReportSubmissionFilePath',
  SubmittedAt: 'SubmittedAt',
  Notes: 'Notes',
  SubmittedBy: 'SubmittedBy',
  TslsToUniDropboxId: 'TslsToUniDropboxId',
  BatchNumber: 'BatchNumber',
  Status: 'Status'
};

exports.Prisma.UniToTslsDropboxScalarFieldEnum = {
  UniToTslsDropboxId: 'UniToTslsDropboxId',
  UniToTslsDropboxName: 'UniToTslsDropboxName',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt',
  OpeningDate: 'OpeningDate',
  ClosingDate: 'ClosingDate',
  IsOpen: 'IsOpen',
  ReportType: 'ReportType',
  UniversityId: 'UniversityId'
};

exports.Prisma.UniToTslsReportSubmissionScalarFieldEnum = {
  UniToTslsReportSubmissionId: 'UniToTslsReportSubmissionId',
  UniToTslsReportSubmissionName: 'UniToTslsReportSubmissionName',
  UniToTslsReportSubmissionFilePath: 'UniToTslsReportSubmissionFilePath',
  SubmittedAt: 'SubmittedAt',
  Notes: 'Notes',
  SubmittedBy: 'SubmittedBy',
  UniToTslsDropboxId: 'UniToTslsDropboxId',
  BatchNumber: 'BatchNumber',
  Status: 'Status'
};

exports.Prisma.ReportSettingsScalarFieldEnum = {
  ReportSettingsId: 'ReportSettingsId',
  ReportType: 'ReportType',
  ReportOpeningDate: 'ReportOpeningDate',
  ReportClosingDate: 'ReportClosingDate',
  IsActive: 'IsActive',
  CreatedBy: 'CreatedBy',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt'
};

exports.Prisma.EmailNotificationScalarFieldEnum = {
  EmailNotificationId: 'EmailNotificationId',
  RecipientEmail: 'RecipientEmail',
  Subject: 'Subject',
  Body: 'Body',
  SentAt: 'SentAt',
  Status: 'Status',
  NotificationType: 'NotificationType',
  RelatedEntityId: 'RelatedEntityId',
  RelatedEntityType: 'RelatedEntityType'
};

exports.Prisma.EmailTemplateScalarFieldEnum = {
  EmailTemplateId: 'EmailTemplateId',
  TemplateName: 'TemplateName',
  TemplateSubject: 'TemplateSubject',
  TemplateBody: 'TemplateBody',
  TemplateType: 'TemplateType',
  IsActive: 'IsActive',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};


exports.Prisma.ModelName = {
  PTEIUser: 'PTEIUser',
  Role: 'Role',
  Department: 'Department',
  Permission: 'Permission',
  RolePermission: 'RolePermission',
  University: 'University',
  UniversityUser: 'UniversityUser',
  Log: 'Log',
  TslsToUniDropbox: 'TslsToUniDropbox',
  TslsToUniReportSubmission: 'TslsToUniReportSubmission',
  UniToTslsDropbox: 'UniToTslsDropbox',
  UniToTslsReportSubmission: 'UniToTslsReportSubmission',
  ReportSettings: 'ReportSettings',
  EmailNotification: 'EmailNotification',
  EmailTemplate: 'EmailTemplate'
};
/**
 * Create the Client
 */
const config = {
  "generator": {
    "name": "client",
    "provider": {
      "fromEnvVar": null,
      "value": "prisma-client-js"
    },
    "output": {
      "value": "C:\\Users\\<USER>\\source\\repos\\PTEI_BACKEND_REVAMPED\\src\\generated\\prisma",
      "fromEnvVar": null
    },
    "config": {
      "engineType": "library"
    },
    "binaryTargets": [
      {
        "fromEnvVar": null,
        "value": "windows",
        "native": true
      }
    ],
    "previewFeatures": [],
    "sourceFilePath": "C:\\Users\\<USER>\\source\\repos\\PTEI_BACKEND_REVAMPED\\prisma\\schema.prisma",
    "isCustomOutput": true
  },
  "relativeEnvPaths": {
    "rootEnvPath": null,
    "schemaEnvPath": "../../../.env"
  },
  "relativePath": "../../../prisma",
  "clientVersion": "6.14.0",
  "engineVersion": "717184b7b35ea05dfa71a3236b7af656013e1e49",
  "datasourceNames": [
    "db"
  ],
  "activeProvider": "sqlserver",
  "inlineDatasources": {
    "db": {
      "url": {
        "fromEnvVar": "DATABASE_URL",
        "value": null
      }
    }
  },
  "inlineSchema": "generator client {\n  provider = \"prisma-client-js\"\n  output   = \"../src/generated/prisma\"\n}\n\ndatasource db {\n  provider = \"sqlserver\"\n  url      = env(\"DATABASE_URL\")\n}\n\nmodel PTEIUser {\n  UserId                       String    @id @default(uuid()) @db.UniqueIdentifier\n  email                        String    @unique @db.NVarChar(1000)\n  HashedPassword               String    @db.NVarChar(1000)\n  FirstName                    String    @default(\"\") @db.NVarChar(1000)\n  LastName                     String    @default(\"\") @db.NVarChar(1000)\n  UserProfilePicPath           String    @default(\"\") @db.NVarChar(1000)\n  UserType                     String?   @db.NVarChar(1000)\n  AccountStatus                String    @default(\"\") @db.NVarChar(1000)\n  IsAccountLockedOut           Boolean   @default(false) @db.Bit\n  FailedLoginAttempts          Int       @default(0)\n  IsUserLoggedIn               Boolean   @default(false) @db.Bit\n  UserCreatedAt                DateTime  @default(now()) @db.DateTimeOffset\n  UserModifiedAt               DateTime? @db.DateTimeOffset\n  EmailVerificationToken       String?   @default(\"\") @db.NVarChar(1000)\n  EmailVerificationTokenExpiry DateTime? @db.DateTimeOffset\n  PasswordResetToken           String?   @default(\"\") @db.NVarChar(1000)\n  PasswordResetTokenExpiry     DateTime? @db.DateTimeOffset\n  RoleId                       String?   @db.UniqueIdentifier\n  DepartmentId                 String?   @db.UniqueIdentifier\n\n  // New registration fields\n  TIN                String?   @db.NVarChar(50)\n  OtherName          String?   @db.NVarChar(1000)\n  PhoneContact       String?   @db.NVarChar(20)\n  Title              String?   @db.NVarChar(20)\n  Gender             String?   @db.NVarChar(50)\n  Ethnicity          String?   @db.NVarChar(100)\n  DateOfBirth        DateTime? @db.Date\n  ResidentialAddress String?   @db.NVarChar(500)\n  PostalAddress      String?   @db.NVarChar(500)\n  Province           String?   @db.NVarChar(100)\n  TSLSScheme         String?   @db.NVarChar(200)\n\n  // Relations\n  Role            Role?            @relation(fields: [RoleId], references: [RoleId])\n  Department      Department?      @relation(fields: [DepartmentId], references: [DepartmentId])\n  Log             Log[]\n  UniversityUsers UniversityUser[]\n  ReportSettings  ReportSettings[]\n\n  @@map(\"PTEIUser\")\n}\n\nmodel Role {\n  RoleId      String   @id @default(uuid()) @db.UniqueIdentifier\n  RoleName    String   @db.NVarChar(1000)\n  Description String?  @db.NVarChar(1000)\n  IsActive    Boolean  @default(true) @db.Bit\n  CreatedAt   DateTime @default(now()) @db.DateTimeOffset\n\n  // Relations\n  Users           PTEIUser[]\n  RolePermissions RolePermission[]\n\n  @@map(\"Role\")\n}\n\nmodel Department {\n  DepartmentId   String   @id @default(uuid()) @db.UniqueIdentifier\n  DepartmentName String   @db.NVarChar(1000)\n  Description    String?  @db.NVarChar(1000)\n  IsActive       Boolean  @default(true) @db.Bit\n  CreatedAt      DateTime @default(now()) @db.DateTimeOffset\n\n  // Relations\n  Users PTEIUser[]\n\n  @@map(\"Department\")\n}\n\nmodel Permission {\n  PermissionId String  @id @default(uuid()) @db.UniqueIdentifier\n  Name         String  @db.NVarChar(1000)\n  Description  String? @db.NVarChar(1000)\n\n  // Relations\n  RolePermissions RolePermission[]\n\n  @@map(\"Permission\")\n}\n\nmodel RolePermission {\n  RoleId       String @db.UniqueIdentifier\n  PermissionId String @db.UniqueIdentifier\n\n  // Relations\n  Role       Role       @relation(fields: [RoleId], references: [RoleId])\n  Permission Permission @relation(fields: [PermissionId], references: [PermissionId])\n\n  @@id([RoleId, PermissionId])\n  @@map(\"RolePermission\")\n}\n\nmodel University {\n  UniversityId   String   @id @default(uuid()) @db.UniqueIdentifier\n  UniversityName String   @db.NVarChar(1000)\n  PrimaryEmail   String   @db.NVarChar(1000)\n  CCEmails       String?  @db.NVarChar(1000)\n  BCCEmails      String?  @db.NVarChar(1000)\n  ContactPerson  String   @db.NVarChar(1000)\n  Phone          String   @db.NVarChar(1000)\n  IsActive       Boolean  @default(true) @db.Bit\n  CreatedAt      DateTime @default(now()) @db.DateTimeOffset\n  UpdatedAt      DateTime @updatedAt @db.DateTimeOffset\n\n  // Relations\n  UniversityUsers    UniversityUser[]\n  TslsToUniDropboxes TslsToUniDropbox[]\n  UniToTslsDropboxes UniToTslsDropbox[]\n\n  @@map(\"University\")\n}\n\nmodel UniversityUser {\n  UniversityUserId String   @id @default(uuid()) @db.UniqueIdentifier\n  CreatedAt        DateTime @default(now()) @db.DateTimeOffset\n  IsActive         Boolean  @default(true) @db.Bit\n  UserId           String   @db.UniqueIdentifier\n  UniversityId     String   @db.UniqueIdentifier\n\n  // Relations\n  User       PTEIUser   @relation(fields: [UserId], references: [UserId])\n  University University @relation(fields: [UniversityId], references: [UniversityId])\n\n  @@map(\"UniversityUser\")\n}\n\nmodel Log {\n  id             String    @id @default(uuid())\n  description    String\n  ipAddress      String\n  timeStamp      DateTime  @default(now())\n  deviceInfo     String?\n  browserInfo    String?\n  actionType     String\n  PTEIUser       PTEIUser? @relation(fields: [pTEIUserUserId], references: [UserId])\n  pTEIUserUserId String?   @db.UniqueIdentifier\n}\n\nmodel TslsToUniDropbox {\n  TslsToUniDropboxId   String    @id @default(uuid()) @db.UniqueIdentifier\n  TslsToUniDropboxName String    @db.NVarChar(1000)\n  UniversityId         String    @db.UniqueIdentifier\n  Year                 Int\n  ModeOfStudy          String    @db.NVarChar(1000)\n  Term                 Int\n  BatchNumber          Int\n  CreatedAt            DateTime  @default(now()) @db.DateTimeOffset\n  UpdatedAt            DateTime? @db.DateTimeOffset\n  OpeningDate          DateTime  @db.DateTimeOffset\n  ClosingDate          DateTime  @db.DateTimeOffset\n  IsOpenStatus         Boolean   @db.Bit\n  ReportType           String    @db.NVarChar(100) // Add this field\n\n  // Relations\n  University                 University                  @relation(fields: [UniversityId], references: [UniversityId])\n  TslsToUniReportSubmissions TslsToUniReportSubmission[]\n\n  @@map(\"TslsToUniDropbox\")\n}\n\nmodel TslsToUniReportSubmission {\n  TslsToUniReportSubmissionId       String   @id @default(uuid()) @db.UniqueIdentifier\n  TslsToUniReportSubmissionName     String   @db.NVarChar(1000)\n  TslsToUniReportSubmissionFilePath String   @db.NVarChar(1000)\n  SubmittedAt                       DateTime @default(now()) @db.DateTimeOffset\n  Notes                             String   @default(\"\") @db.NVarChar(1000)\n  SubmittedBy                       String   @db.NVarChar(1000)\n  TslsToUniDropboxId                String   @db.UniqueIdentifier\n  BatchNumber                       String?  @db.NVarChar(100) // Add this field\n  Status                            String   @default(\"Pending\") @db.NVarChar(50) // Add this field\n\n  // Relations\n  TslsToUniDropbox TslsToUniDropbox @relation(fields: [TslsToUniDropboxId], references: [TslsToUniDropboxId])\n\n  @@map(\"TslsToUniReportSubmission\")\n}\n\nmodel UniToTslsDropbox {\n  UniToTslsDropboxId   String    @id @default(uuid()) @db.UniqueIdentifier\n  UniToTslsDropboxName String    @db.NVarChar(1000)\n  CreatedAt            DateTime  @default(now()) @db.DateTimeOffset\n  UpdatedAt            DateTime? @db.DateTimeOffset\n  OpeningDate          DateTime  @db.DateTimeOffset\n  ClosingDate          DateTime  @db.DateTimeOffset\n  IsOpen               Boolean   @db.Bit\n  ReportType           String    @db.NVarChar(100) // Add this field\n  UniversityId         String    @db.UniqueIdentifier // Add this field\n\n  // Relations\n  University                 University                  @relation(fields: [UniversityId], references: [UniversityId])\n  UniToTslsReportSubmissions UniToTslsReportSubmission[]\n\n  @@map(\"UniToTslsDropbox\")\n}\n\nmodel UniToTslsReportSubmission {\n  UniToTslsReportSubmissionId       String   @id @default(uuid()) @db.UniqueIdentifier\n  UniToTslsReportSubmissionName     String   @db.NVarChar(1000)\n  UniToTslsReportSubmissionFilePath String   @db.NVarChar(1000)\n  SubmittedAt                       DateTime @default(now()) @db.DateTimeOffset\n  Notes                             String   @default(\"\") @db.NVarChar(1000)\n  SubmittedBy                       String   @db.NVarChar(1000)\n  UniToTslsDropboxId                String   @db.UniqueIdentifier\n  BatchNumber                       String?  @db.NVarChar(100) // Add this field\n  Status                            String   @default(\"Pending\") @db.NVarChar(50) // Add this field\n\n  // Relations\n  UniToTslsDropbox UniToTslsDropbox @relation(fields: [UniToTslsDropboxId], references: [UniToTslsDropboxId])\n\n  @@map(\"UniToTslsReportSubmission\")\n}\n\n// Add this new model for Report Settings\nmodel ReportSettings {\n  ReportSettingsId  String   @id @default(uuid()) @db.UniqueIdentifier\n  ReportType        String   @db.NVarChar(100)\n  ReportOpeningDate DateTime @db.DateTimeOffset\n  ReportClosingDate DateTime @db.DateTimeOffset\n  IsActive          Boolean  @default(true) @db.Bit\n  CreatedBy         String   @db.UniqueIdentifier\n  CreatedAt         DateTime @default(now()) @db.DateTimeOffset\n  UpdatedAt         DateTime @updatedAt @db.DateTimeOffset\n\n  // Relations\n  Creator PTEIUser @relation(fields: [CreatedBy], references: [UserId])\n\n  @@unique([ReportType])\n  @@map(\"ReportSettings\")\n}\n\n// Add this new model for Email Notifications\nmodel EmailNotification {\n  EmailNotificationId String   @id @default(uuid()) @db.UniqueIdentifier\n  RecipientEmail      String   @db.NVarChar(1000)\n  Subject             String   @db.NVarChar(1000)\n  Body                String   @db.NVarChar(4000)\n  SentAt              DateTime @default(now()) @db.DateTimeOffset\n  Status              String   @db.NVarChar(50)\n  NotificationType    String   @db.NVarChar(100)\n  RelatedEntityId     String?  @db.UniqueIdentifier\n  RelatedEntityType   String?  @db.NVarChar(100)\n\n  @@map(\"EmailNotification\")\n}\n\n// Add this new model for Email Templates\nmodel EmailTemplate {\n  EmailTemplateId String   @id @default(uuid()) @db.UniqueIdentifier\n  TemplateName    String   @db.NVarChar(200)\n  TemplateSubject String   @db.NVarChar(500)\n  TemplateBody    String   @db.NVarChar(4000)\n  TemplateType    String   @db.NVarChar(100)\n  IsActive        Boolean  @default(true)\n  CreatedAt       DateTime @default(now()) @db.DateTimeOffset\n  UpdatedAt       DateTime @updatedAt @db.DateTimeOffset\n\n  @@map(\"EmailTemplate\")\n}\n",
  "inlineSchemaHash": "05d91829c54bb6537ce6b054b5e18248f26248feea8f19b634bd9d9a99755a42",
  "copyEngine": true
}

const fs = require('fs')

config.dirname = __dirname
if (!fs.existsSync(path.join(__dirname, 'schema.prisma'))) {
  const alternativePaths = [
    "src/generated/prisma",
    "generated/prisma",
  ]
  
  const alternativePath = alternativePaths.find((altPath) => {
    return fs.existsSync(path.join(process.cwd(), altPath, 'schema.prisma'))
  }) ?? alternativePaths[0]

  config.dirname = path.join(process.cwd(), alternativePath)
  config.isBundled = true
}

config.runtimeDataModel = JSON.parse("{\"models\":{\"PTEIUser\":{\"dbName\":\"PTEIUser\",\"schema\":null,\"fields\":[{\"name\":\"UserId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"email\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"HashedPassword\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"FirstName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"default\":\"\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"LastName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"default\":\"\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UserProfilePicPath\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"default\":\"\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UserType\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"AccountStatus\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"default\":\"\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"IsAccountLockedOut\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":[\"Bit\",[]],\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"FailedLoginAttempts\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"nativeType\":null,\"default\":0,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"IsUserLoggedIn\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":[\"Bit\",[]],\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UserCreatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UserModifiedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"EmailVerificationToken\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"default\":\"\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"EmailVerificationTokenExpiry\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"PasswordResetToken\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"default\":\"\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"PasswordResetTokenExpiry\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"RoleId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"DepartmentId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"TIN\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"50\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"OtherName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"PhoneContact\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"20\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Title\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"20\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Gender\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"50\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Ethnicity\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"100\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"DateOfBirth\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"Date\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ResidentialAddress\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"500\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"PostalAddress\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"500\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Province\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"100\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"TSLSScheme\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"200\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Role\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Role\",\"nativeType\":null,\"relationName\":\"PTEIUserToRole\",\"relationFromFields\":[\"RoleId\"],\"relationToFields\":[\"RoleId\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Department\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Department\",\"nativeType\":null,\"relationName\":\"DepartmentToPTEIUser\",\"relationFromFields\":[\"DepartmentId\"],\"relationToFields\":[\"DepartmentId\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Log\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Log\",\"nativeType\":null,\"relationName\":\"LogToPTEIUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UniversityUsers\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UniversityUser\",\"nativeType\":null,\"relationName\":\"PTEIUserToUniversityUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ReportSettings\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ReportSettings\",\"nativeType\":null,\"relationName\":\"PTEIUserToReportSettings\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Role\":{\"dbName\":\"Role\",\"schema\":null,\"fields\":[{\"name\":\"RoleId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"RoleName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"IsActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":[\"Bit\",[]],\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"CreatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Users\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PTEIUser\",\"nativeType\":null,\"relationName\":\"PTEIUserToRole\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"RolePermissions\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"RolePermission\",\"nativeType\":null,\"relationName\":\"RoleToRolePermission\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Department\":{\"dbName\":\"Department\",\"schema\":null,\"fields\":[{\"name\":\"DepartmentId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"DepartmentName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"IsActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":[\"Bit\",[]],\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"CreatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Users\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PTEIUser\",\"nativeType\":null,\"relationName\":\"DepartmentToPTEIUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Permission\":{\"dbName\":\"Permission\",\"schema\":null,\"fields\":[{\"name\":\"PermissionId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"RolePermissions\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"RolePermission\",\"nativeType\":null,\"relationName\":\"PermissionToRolePermission\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"RolePermission\":{\"dbName\":\"RolePermission\",\"schema\":null,\"fields\":[{\"name\":\"RoleId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"PermissionId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Role\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Role\",\"nativeType\":null,\"relationName\":\"RoleToRolePermission\",\"relationFromFields\":[\"RoleId\"],\"relationToFields\":[\"RoleId\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Permission\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Permission\",\"nativeType\":null,\"relationName\":\"PermissionToRolePermission\",\"relationFromFields\":[\"PermissionId\"],\"relationToFields\":[\"PermissionId\"],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":{\"name\":null,\"fields\":[\"RoleId\",\"PermissionId\"]},\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"University\":{\"dbName\":\"University\",\"schema\":null,\"fields\":[{\"name\":\"UniversityId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UniversityName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"PrimaryEmail\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"CCEmails\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"BCCEmails\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ContactPerson\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Phone\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"IsActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":[\"Bit\",[]],\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"CreatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UpdatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"UniversityUsers\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UniversityUser\",\"nativeType\":null,\"relationName\":\"UniversityToUniversityUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"TslsToUniDropboxes\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"TslsToUniDropbox\",\"nativeType\":null,\"relationName\":\"TslsToUniDropboxToUniversity\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UniToTslsDropboxes\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UniToTslsDropbox\",\"nativeType\":null,\"relationName\":\"UniToTslsDropboxToUniversity\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UniversityUser\":{\"dbName\":\"UniversityUser\",\"schema\":null,\"fields\":[{\"name\":\"UniversityUserId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"CreatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"IsActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":[\"Bit\",[]],\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UserId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UniversityId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"User\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PTEIUser\",\"nativeType\":null,\"relationName\":\"PTEIUserToUniversityUser\",\"relationFromFields\":[\"UserId\"],\"relationToFields\":[\"UserId\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"University\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"University\",\"nativeType\":null,\"relationName\":\"UniversityToUniversityUser\",\"relationFromFields\":[\"UniversityId\"],\"relationToFields\":[\"UniversityId\"],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Log\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ipAddress\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"timeStamp\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"deviceInfo\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"browserInfo\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"actionType\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"PTEIUser\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PTEIUser\",\"nativeType\":null,\"relationName\":\"LogToPTEIUser\",\"relationFromFields\":[\"pTEIUserUserId\"],\"relationToFields\":[\"UserId\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"pTEIUserUserId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"TslsToUniDropbox\":{\"dbName\":\"TslsToUniDropbox\",\"schema\":null,\"fields\":[{\"name\":\"TslsToUniDropboxId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"TslsToUniDropboxName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UniversityId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Year\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ModeOfStudy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Term\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"BatchNumber\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"CreatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UpdatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"OpeningDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ClosingDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"IsOpenStatus\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":[\"Bit\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ReportType\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"100\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"University\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"University\",\"nativeType\":null,\"relationName\":\"TslsToUniDropboxToUniversity\",\"relationFromFields\":[\"UniversityId\"],\"relationToFields\":[\"UniversityId\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"TslsToUniReportSubmissions\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"TslsToUniReportSubmission\",\"nativeType\":null,\"relationName\":\"TslsToUniDropboxToTslsToUniReportSubmission\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"TslsToUniReportSubmission\":{\"dbName\":\"TslsToUniReportSubmission\",\"schema\":null,\"fields\":[{\"name\":\"TslsToUniReportSubmissionId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"TslsToUniReportSubmissionName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"TslsToUniReportSubmissionFilePath\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"SubmittedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"default\":\"\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"SubmittedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"TslsToUniDropboxId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"BatchNumber\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"100\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Status\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"50\"]],\"default\":\"Pending\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"TslsToUniDropbox\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"TslsToUniDropbox\",\"nativeType\":null,\"relationName\":\"TslsToUniDropboxToTslsToUniReportSubmission\",\"relationFromFields\":[\"TslsToUniDropboxId\"],\"relationToFields\":[\"TslsToUniDropboxId\"],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UniToTslsDropbox\":{\"dbName\":\"UniToTslsDropbox\",\"schema\":null,\"fields\":[{\"name\":\"UniToTslsDropboxId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UniToTslsDropboxName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"CreatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UpdatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"OpeningDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ClosingDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"IsOpen\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Boolean\",\"nativeType\":[\"Bit\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ReportType\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"100\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UniversityId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"University\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"University\",\"nativeType\":null,\"relationName\":\"UniToTslsDropboxToUniversity\",\"relationFromFields\":[\"UniversityId\"],\"relationToFields\":[\"UniversityId\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UniToTslsReportSubmissions\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UniToTslsReportSubmission\",\"nativeType\":null,\"relationName\":\"UniToTslsDropboxToUniToTslsReportSubmission\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"UniToTslsReportSubmission\":{\"dbName\":\"UniToTslsReportSubmission\",\"schema\":null,\"fields\":[{\"name\":\"UniToTslsReportSubmissionId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UniToTslsReportSubmissionName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UniToTslsReportSubmissionFilePath\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"SubmittedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"default\":\"\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"SubmittedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UniToTslsDropboxId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"BatchNumber\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"100\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Status\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"50\"]],\"default\":\"Pending\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UniToTslsDropbox\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"UniToTslsDropbox\",\"nativeType\":null,\"relationName\":\"UniToTslsDropboxToUniToTslsReportSubmission\",\"relationFromFields\":[\"UniToTslsDropboxId\"],\"relationToFields\":[\"UniToTslsDropboxId\"],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"ReportSettings\":{\"dbName\":\"ReportSettings\",\"schema\":null,\"fields\":[{\"name\":\"ReportSettingsId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ReportType\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"100\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ReportOpeningDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ReportClosingDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"IsActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":[\"Bit\",[]],\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"CreatedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"CreatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UpdatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"Creator\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PTEIUser\",\"nativeType\":null,\"relationName\":\"PTEIUserToReportSettings\",\"relationFromFields\":[\"CreatedBy\"],\"relationToFields\":[\"UserId\"],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"ReportType\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"ReportType\"]}],\"isGenerated\":false},\"EmailNotification\":{\"dbName\":\"EmailNotification\",\"schema\":null,\"fields\":[{\"name\":\"EmailNotificationId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"RecipientEmail\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Subject\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"1000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Body\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"4000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"SentAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"Status\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"50\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"NotificationType\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"100\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"RelatedEntityId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"RelatedEntityType\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"100\"]],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"EmailTemplate\":{\"dbName\":\"EmailTemplate\",\"schema\":null,\"fields\":[{\"name\":\"EmailTemplateId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":[\"UniqueIdentifier\",[]],\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"TemplateName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"200\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"TemplateSubject\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"500\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"TemplateBody\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"4000\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"TemplateType\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":[\"NVarChar\",[\"100\"]],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"IsActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"nativeType\":null,\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"CreatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"UpdatedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":[\"DateTimeOffset\",[]],\"isGenerated\":false,\"isUpdatedAt\":true}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false}},\"enums\":{},\"types\":{}}")
defineDmmfProperty(exports.Prisma, config.runtimeDataModel)
config.engineWasm = undefined
config.compilerWasm = undefined


const { warnEnvConflicts } = require('./runtime/library.js')

warnEnvConflicts({
    rootEnvPath: config.relativeEnvPaths.rootEnvPath && path.resolve(config.dirname, config.relativeEnvPaths.rootEnvPath),
    schemaEnvPath: config.relativeEnvPaths.schemaEnvPath && path.resolve(config.dirname, config.relativeEnvPaths.schemaEnvPath)
})

const PrismaClient = getPrismaClient(config)
exports.PrismaClient = PrismaClient
Object.assign(exports, Prisma)

// file annotations for bundling tools to include these files
path.join(__dirname, "query_engine-windows.dll.node");
path.join(process.cwd(), "src/generated/prisma/query_engine-windows.dll.node")
// file annotations for bundling tools to include these files
path.join(__dirname, "schema.prisma");
path.join(process.cwd(), "src/generated/prisma/schema.prisma")
