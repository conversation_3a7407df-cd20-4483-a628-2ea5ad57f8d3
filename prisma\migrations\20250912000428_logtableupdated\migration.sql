/*
  Warnings:

  - The primary key for the `Log` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `ActionType` on the `Log` table. All the data in the column will be lost.
  - You are about to drop the column `AdditionalInfo` on the `Log` table. All the data in the column will be lost.
  - You are about to drop the column `IPAddress` on the `Log` table. All the data in the column will be lost.
  - You are about to drop the column `LogId` on the `Log` table. All the data in the column will be lost.
  - You are about to drop the column `NewValues` on the `Log` table. All the data in the column will be lost.
  - You are about to drop the column `OldValues` on the `Log` table. All the data in the column will be lost.
  - You are about to drop the column `RecordId` on the `Log` table. All the data in the column will be lost.
  - You are about to drop the column `SessionId` on the `Log` table. All the data in the column will be lost.
  - You are about to drop the column `TableName` on the `Log` table. All the data in the column will be lost.
  - You are about to drop the column `Timestamp` on the `Log` table. All the data in the column will be lost.
  - You are about to drop the column `UserAgent` on the `Log` table. All the data in the column will be lost.
  - You are about to drop the column `UserId` on the `Log` table. All the data in the column will be lost.
  - Added the required column `actionType` to the `Log` table without a default value. This is not possible if the table is not empty.
  - Added the required column `description` to the `Log` table without a default value. This is not possible if the table is not empty.
  - The required column `id` was added to the `Log` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - Added the required column `ipAddress` to the `Log` table without a default value. This is not possible if the table is not empty.

*/
BEGIN TRY

BEGIN TRAN;

-- RedefineTables
BEGIN TRANSACTION;
DECLARE @SQL NVARCHAR(MAX) = N''
SELECT @SQL += N'ALTER TABLE '
    + QUOTENAME(OBJECT_SCHEMA_NAME(PARENT_OBJECT_ID))
    + '.'
    + QUOTENAME(OBJECT_NAME(PARENT_OBJECT_ID))
    + ' DROP CONSTRAINT '
    + OBJECT_NAME(OBJECT_ID) + ';'
FROM SYS.OBJECTS
WHERE TYPE_DESC LIKE '%CONSTRAINT'
    AND OBJECT_NAME(PARENT_OBJECT_ID) = 'Log'
    AND SCHEMA_NAME(SCHEMA_ID) = 'dbo'
EXEC sp_executesql @SQL
;
CREATE TABLE [dbo].[_prisma_new_Log] (
    [id] NVARCHAR(1000) NOT NULL,
    [description] NVARCHAR(1000) NOT NULL,
    [ipAddress] NVARCHAR(1000) NOT NULL,
    [timeStamp] DATETIME2 NOT NULL CONSTRAINT [Log_timeStamp_df] DEFAULT CURRENT_TIMESTAMP,
    [deviceInfo] NVARCHAR(1000),
    [browserInfo] NVARCHAR(1000),
    [actionType] NVARCHAR(1000) NOT NULL,
    [pTEIUserUserId] UNIQUEIDENTIFIER,
    CONSTRAINT [Log_pkey] PRIMARY KEY CLUSTERED ([id])
);
IF EXISTS(SELECT * FROM [dbo].[Log])
    EXEC('INSERT INTO [dbo].[_prisma_new_Log] () SELECT  FROM [dbo].[Log] WITH (holdlock tablockx)');
DROP TABLE [dbo].[Log];
EXEC SP_RENAME N'dbo._prisma_new_Log', N'Log';
COMMIT;

COMMIT TRAN;

END TRY
BEGIN CATCH

IF @@TRANCOUNT > 0
BEGIN
    ROLLBACK TRAN;
END;
THROW

END CATCH
