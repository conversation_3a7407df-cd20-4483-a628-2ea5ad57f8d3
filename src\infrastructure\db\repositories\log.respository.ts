// ---------- FILE PATH
// src/infrastructure/repositories/log.repository.ts

// ---------- DESCRIPTION
// This file contains the log repository for interacting with the log data source.

// Import PrismaClient
import { PrismaClient } from "../../../generated/prisma/index.js";

// Import the required interfaces.
import { ILogRepository } from "../../../";

// Import the log entity.
import { Log } from "../../../domain/Log.entity.js";

// Initialize PrismaClient
const prisma = new PrismaClient();

export class LogRepository implements ILogRepository {
  async create(log: Log): Promise<Log> {
    const created = await prisma.log.create({
      data: {
        id: log.id,
        description: log.description,
        ipAddress: log.ipAddress,
        timeStamp: log.timeStamp,
        deviceInfo: log.deviceInfo ?? undefined,
        browserInfo: log.browserInfo ?? undefined,
        actionType: log.actionType,
      },
    });

    return new Log(
      created.id,
      created.description,
      created.ipAddress,
      created.timeStamp,
      created.deviceInfo ?? undefined,
      created.browserInfo ?? undefined,
      created.actionType
    );
  }

  async findAll(): Promise<Log[]> {
    const logs = await prisma.log.findMany();
    return logs.map(
      (l) =>
        new Log(
          l.id,
          l.description,
          l.ipAddress,
          l.timeStamp,
          l.deviceInfo ?? undefined,
          l.browserInfo ?? undefined,
          l.actionType
        )
    );
  }

  async findById(id: string): Promise<Log | null> {
    const log = await prisma.log.findUnique({ where: { id } });
    if (!log) return null;
    return new Log(
      log.id,
      log.description,
      log.ipAddress,
      log.timeStamp,
      log.deviceInfo ?? undefined,
      log.browserInfo ?? undefined,
      log.actionType
    );
  }
}