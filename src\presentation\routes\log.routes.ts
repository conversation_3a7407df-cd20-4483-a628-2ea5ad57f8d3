// ---------- FILE PATH
// src/presentation/routes/log.routes.ts

import { FastifyInstance, FastifyRequest, FastifyReply } from "fastify";
import { LogRepository } from "../../infrastructure/repositories/LogRepository.js";
import { LogService } from "../../application/services/Log.Service.js";
import { LogController } from "../controllers/Log.Controller.js";
import { CreateLogJsonSchema } from "../../application/dto/Log.dto.js";

export async function logRoutes(fastify: FastifyInstance) {
  const repo = new LogRepository();
  const service = new LogService(repo);
  const controller = new LogController(service);

  // Create a new log entry
  // POST /create-log
  fastify.post(
    "/create-log",
    {
      schema: {
        body: CreateLogJsonSchema,
        response: {
          201: {
            type: "object",
            properties: {
              id: { type: "string" },
              description: { type: "string" },
              ipAddress: { type: "string" },
              deviceInfo: { type: "string" },
              browserInfo: { type: "string" },
              actionType: { type: "string" },
              createdAt: { type: "string", format: "date-time" },
            },
          },
          500: {
            type: "object",
            properties: {
              error: { type: "string" },
            },
          },
        },
        tags: ["Logs"],
        description: "Create a new log entry",
      },
    },
    controller.create.bind(controller)
  );
}