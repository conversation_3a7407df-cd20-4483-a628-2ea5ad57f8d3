// ---------- FILE PATH
// src/application/interfaces/iservices/ilog.service.ts

// ---------- DESCRIPTION
// This interface defines the contract for the Log service, outlining the methods available for interacting with log entries.

// Import the log entity.
import { Log } from "../../../domain/Log.entity.js";

export interface ILogService {
  /*
   * Create a new log entry.
   * @param description - The description of the log entry.
   * @param ipAddress - The IP address of the user.
   * @param actionType - The type of action being logged.
   * @param deviceInfo - Optional device information.
   * @returns The created log entry.
   */
  createLog(
    description: string,
    ipAddress: string,
    actionType: string,
    deviceInfo?: string,
    browserInfo?: string,
    geoLocation?: string
  ): Promise<Log>;

  /*
   * Create a new log entry from a request.
   * @param req - The Fastify request object.
   * @param description - The description of the log entry.
   * @param geoLocation - Optional geo-location information.
   * @returns The created log entry.
   */
  createLogFromRequest(
    req: any, // FastifyRequest
    description: string,
    geoLocation?: string
  ): Promise<Log>;
}