// src/presentation/controllers/AuthController.ts
import { FastifyRequest, FastifyReply } from 'fastify';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { CryptoPasswordHasher } from '../../application/services/Password.Hasher.js';

// Audit Service Interface
export interface IAuditService {
  logAsync(
    userId: string,
    actionType: string,
    tableName: string,
    recordId: string,
    oldValues?: string,
    newValues?: string,
    additionalInfo?: string
  ): Promise<void>;
}

// Simple audit service implementation
class SimpleAuditService implements IAuditService {
  constructor(private prisma: any) {}

  async logAsync(
    userId: string,
    actionType: string,
    tableName: string,
    recordId: string,
    oldValues?: string,
    newValues?: string,
    additionalInfo?: string
  ): Promise<void> {
    // Create a meaningful description from the provided parameters
    let description = `${actionType} action on ${tableName} for record ${recordId}`;
    if (oldValues) description += `. Previous: ${oldValues}`;
    if (newValues) description += `. New: ${newValues}`;
    if (additionalInfo) description += `. Info: ${additionalInfo}`;

    await this.prisma.log.create({
      data: {
        id: crypto.randomUUID(),
        description: description,
        ipAddress: '127.0.0.1',
        actionType: actionType,
        deviceInfo: 'Server',
        browserInfo: 'API',
        pTEIUserUserId: userId
      }
    });
  }
}

export class AuthController {
  private prisma: any;
  private config: any;
  private auditService: IAuditService;
  private passwordHasher: CryptoPasswordHasher;

  constructor(prisma: any, config: any) {
    this.prisma = prisma;
    this.config = config;
    this.passwordHasher = new CryptoPasswordHasher();
    this.auditService = new SimpleAuditService(prisma);
  }

  // ===== LOGIN =====
  async login(request: FastifyRequest<{ Body: { email: string; password: string } }>, reply: FastifyReply) {
    console.log('🚀 Login attempt started');
    try {
      const { email, password } = request.body;
      console.log('📧 Login request for email:', email);

      if (!email || !password) {
        console.log('❌ Missing email or password');
        return reply.status(400).send({ error: 'Invalid input.' });
      }

      console.log('🔍 Searching for user in database...');
      // 1. Get user by email
      const user = await this.prisma.pTEIUser.findUnique({
        where: { email: email },
        include: { Role: true, Department: true }
      });

      if (!user) {
        console.log('❌ User not found for email:', email);
        return reply.status(401).send({ error: 'Invalid credentials' });
      }

      console.log('✅ User found:', {
        userId: user.UserId,
        email: user.email,
        hasRole: !!user.Role,
        roleName: user.Role?.RoleName,
        accountStatus: user.AccountStatus,
        isLocked: user.IsAccountLockedOut,
        failedAttempts: user.FailedLoginAttempts
      });

      // 2. Check lockout / inactive
      console.log('🔒 Checking account status...');
      if (user.IsAccountLockedOut) {
        console.log('❌ Account is locked');
        return reply.status(403).send({ error: 'Account is locked due to too many failed login attempts.' });
      }

      if (user.AccountStatus?.toLowerCase() !== 'active') {
        console.log('❌ Account is not active. Status:', user.AccountStatus);
        return reply.status(403).send({ error: 'Account is not active.' });
      }

      // 3. Validate password
      console.log('🔐 Validating password...');
      const hashedInput = this.passwordHasher.hash(password);
      console.log('Password comparison:', {
        storedHash: user.HashedPassword?.substring(0, 20) + '...',
        inputHash: hashedInput?.substring(0, 20) + '...',
        match: user.HashedPassword === hashedInput
      });

      if (user.HashedPassword !== hashedInput) {
        console.log('❌ Password mismatch');
        const failedAttempts = (user.FailedLoginAttempts || 0) + 1;
        const isLocked = failedAttempts >= 5;

        await this.prisma.pTEIUser.update({
          where: { UserId: user.UserId },
          data: {
            FailedLoginAttempts: failedAttempts,
            IsAccountLockedOut: isLocked
          }
        });

        await this.auditService.logAsync(
          user.UserId,
          'LOGIN_FAILED',
          'PTEIUser',
          user.UserId,
          'Invalid login'
        );

        return reply.status(401).send({ error: 'Invalid credentials.' });
      }

      // 4. Success → reset attempts & set logged in
      console.log('✅ Password valid, updating user status...');
      await this.prisma.pTEIUser.update({
        where: { UserId: user.UserId },
        data: { FailedLoginAttempts: 0, IsUserLoggedIn: true }
      });

      console.log('📝 Logging audit event...');
      await this.auditService.logAsync(
        user.UserId,
        'LOGIN_SUCCESS',
        'PTEIUser',
        user.UserId,
        'User logged in'
      );

      // 5. Generate JWT
      console.log('🎫 Generating JWT token...');
      const token = this.generateJwtToken(user);
      console.log('✅ JWT token generated successfully, length:', token?.length);

      const response = {
        token: token,
        user: {
          userId: user.UserId,
          email: user.email,
          firstName: user.FirstName,
          lastName: user.LastName,
          roleId: user.RoleId,
          roleName: user.Role?.RoleName,
          departmentId: user.DepartmentId,
          departmentName: user.Department?.DepartmentName,
          userType: user.UserType || null
        }
      };

      console.log('📤 Sending response with keys:', Object.keys(response));
      console.log('📤 User object keys:', Object.keys(response.user));
      console.log('📤 Response status: 200');

      const result = reply.status(200).send(response);
      console.log('📤 Reply sent, result:', typeof result);
      return result;

    } catch (error) {
      console.error('💥 Login error occurred:', error);
      console.error('💥 Error type:', typeof error);
      console.error('💥 Error message:', error instanceof Error ? error.message : String(error));
      console.error('💥 Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      return reply.status(500).send({ error: 'Internal server error during login' });
    }
  }

  // ===== LOGOUT =====
  async logout(request: FastifyRequest, reply: FastifyReply) {
    const userId = request.user?.userId;
    if (!userId) {
      return reply.status(401).send({ error: 'Unauthorized' });
    }

    const user = await this.prisma.pTEIUser.findUnique({ where: { UserId: userId } });
    if (!user) {
      return reply.status(404).send({ error: 'User not found' });
    }

    await this.prisma.pTEIUser.update({
      where: { UserId: userId },
      data: { IsUserLoggedIn: false }
    });

    await this.auditService.logAsync(
      user.UserId,
      'LOGOUT',
      'PTEIUser',
      user.UserId,
      'User logged out'
    );

    return reply.status(200).send({ message: 'Logged out' });
  }

  // ===== JWT Generator =====
  private generateJwtToken(user: any): string {
    try {
      if (!user.Role || !user.Role.RoleName) {
        console.error('Role is missing for user:', { userId: user.UserId, email: user.email, role: user.Role });
        throw new Error(`Role is not set for user ${user.email}`);
      }

      if (!this.config.JWT_SECRET) {
        console.error('JWT_SECRET is not configured');
        throw new Error('JWT_SECRET is not configured');
      }

      const payload = {
        sub: user.UserId,
        email: user.email,
        role: user.Role.RoleName,
        userType: user.UserType || null
      };

      console.log('Generating JWT with payload:', payload);
      return jwt.sign(payload, this.config.JWT_SECRET, { expiresIn: '8h' });
    } catch (error) {
      console.error('JWT generation error:', error);
      throw error;
    }
  }
}
