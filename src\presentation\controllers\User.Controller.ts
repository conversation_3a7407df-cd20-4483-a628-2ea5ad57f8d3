// User Controller - HTTP endpoints for user operations
import { FastifyRequest, FastifyReply } from 'fastify';
import { UserService } from '../../application/services/User.Service.js';
import { IAuditService } from '../../application/interfaces/IServices.js';
import { CryptoPasswordHasher } from '../../application/services/Password.Hasher.js';
import {
  CreateUserDto,
  UpdateUserDto,
  ChangeStatusDto,
  UnlockAccountDto,
  ResetPasswordDto,
  VerifyEmailDto
} from '../../application/dto/user.dto.js';
import { ValidationError } from '../../application/validation/DtoValidator.js';
import crypto from 'crypto';

// Simple audit service implementation
class SimpleAuditService implements IAuditService {
  constructor(private prisma: any) {}

  async logAsync(
    userId: string,
    actionType: string,
    tableName: string,
    recordId: string,
    oldValues?: string,
    newValues?: string,
    additionalInfo?: string
  ): Promise<void> {
    try {
      await this.prisma.auditLog.create({
        data: {
          AuditLogId: crypto.randomUUID(),
          UserId: userId,
          ActionType: actionType,
          TableName: tableName,
          RecordId: recordId,
          OldValues: oldValues,
          NewValues: newValues,
          AdditionalInfo: additionalInfo,
          Timestamp: new Date()
        }
      });
    } catch (error) {
      console.error('Failed to log audit:', error);
      // Don't throw - audit logging shouldn't break the main operation
    }
  }
}

export class UserController {
  private userService: UserService;

  constructor(prisma: any) {
    const passwordHasher = new CryptoPasswordHasher();
    const auditService = new SimpleAuditService(prisma);
    this.userService = new UserService(prisma, passwordHasher, auditService);
  }

  /**
   * Create a new user
   * POST /api/users
   */
  async createUser(request: FastifyRequest<{ Body: CreateUserDto }>, reply: FastifyReply) {
    try {
      console.log('👤 Creating user...');
      console.log('📝 Request data:', request.body);

      const userDto = await this.userService.createUser(request.body);
      console.log('✅ User created successfully:', userDto);

      reply.status(201).send({
        success: true,
        message: 'User created successfully',
        data: userDto
      });
    } catch (error) {
      console.error('💥 User creation error:', error);

      if (error instanceof ValidationError) {
        reply.status(400).send({
          success: false,
          message: 'Validation failed',
          errors: error.errors
        });
      } else {
        reply.status(500).send({
          success: false,
          message: error instanceof Error ? error.message : 'Failed to create user'
        });
      }
    }
  }

  /**
   * Update an existing user
   * PUT /api/users/:id
   */
  async updateUser(request: FastifyRequest<{ Body: Omit<UpdateUserDto, 'userId'>; Params: { id: string } }>, reply: FastifyReply) {
    try {
      const updateDto = { ...request.body, userId: request.params.id };
      const success = await this.userService.updateUserAsync(updateDto);

      if (!success) {
        reply.status(404).send({
          success: false,
          message: 'User not found'
        });
        return;
      }

      // Get updated user for response
      const updatedUser = await this.userService.getUserByIdAsync(request.params.id);
      if (!updatedUser) {
        reply.status(404).send({
          success: false,
          message: 'User not found after update'
        });
        return;
      }

      reply.send({
        success: true,
        message: 'User updated successfully',
        data: {
          userId: updatedUser.userId,
          email: updatedUser.email,
          firstName: updatedUser.firstName,
          lastName: updatedUser.lastName,
          userType: updatedUser.userType,
          accountStatus: updatedUser.accountStatus
        }
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        reply.status(400).send({
          success: false,
          message: 'Validation failed',
          errors: error.errors
        });
      } else {
        reply.status(500).send({
          success: false,
          message: error instanceof Error ? error.message : 'Failed to update user'
        });
      }
    }
  }

  /**
   * Change user status
   * PATCH /api/users/:id/status
   */
  async changeUserStatus(request: FastifyRequest<{ Body: { newStatus: string }; Params: { id: string } }>, reply: FastifyReply) {
    try {
      const changeStatusDto: ChangeStatusDto = {
        userId: request.params.id,
        newStatus: request.body.newStatus
      };
      
      const userDto = await this.userService.changeUserStatus(changeStatusDto);
      
      reply.send({
        success: true,
        message: 'User status changed successfully',
        data: userDto
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        reply.status(400).send({
          success: false,
          message: 'Validation failed',
          errors: error.errors
        });
      } else {
        reply.status(500).send({
          success: false,
          message: error instanceof Error ? error.message : 'Failed to change user status'
        });
      }
    }
  }

  /**
   * Unlock user account
   * PATCH /api/users/:id/unlock
   */
  async unlockAccount(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
    try {
      const unlockDto: UnlockAccountDto = {
        userId: request.params.id
      };
      
      const userDto = await this.userService.unlockAccount(unlockDto);
      
      reply.send({
        success: true,
        message: 'Account unlocked successfully',
        data: userDto
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        reply.status(400).send({
          success: false,
          message: 'Validation failed',
          errors: error.errors
        });
      } else {
        reply.status(500).send({
          success: false,
          message: error instanceof Error ? error.message : 'Failed to unlock account'
        });
      }
    }
  }

  /**
   * Get user by ID
   * GET /api/users/:id
   */
  async getUserById(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
    try {
      const user = await this.userService.getUserByIdAsync(request.params.id);

      if (!user) {
        reply.status(404).send({
          success: false,
          message: 'User not found'
        });
        return;
      }

      reply.send({
        success: true,
        message: 'User retrieved successfully',
        data: {
          userId: user.userId,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          userType: user.userType,
          accountStatus: user.accountStatus,
          userCreatedAt: user.userCreatedAt,
          userModifiedAt: user.userModifiedAt,
          isAccountLockedOut: user.isAccountLockedOut,
          failedLoginAttempts: user.failedLoginAttempts,
          userProfilePicPath: user.userProfilePicPath
        }
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get user'
      });
    }
  }

  /**
   * Get all users with pagination
   * GET /api/users
   */
  async getUsers(request: FastifyRequest<{ Querystring: { page?: string; limit?: string } }>, reply: FastifyReply) {
    try {
      const [users, statistics] = await Promise.all([
        this.userService.getAllUsersAsync(),
        this.userService.getUserStatistics()
      ]);

      // Simple pagination (you might want to implement this in the service)
      const page = parseInt(request.query.page || '1');
      const limit = parseInt(request.query.limit || '10');
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;

      const paginatedUsers = users.slice(startIndex, endIndex);

      reply.send({
        success: true,
        message: 'Users retrieved successfully',
        data: {
          users: paginatedUsers,
          pagination: {
            page,
            limit,
            total: users.length,
            totalPages: Math.ceil(users.length / limit)
          },
          statistics: {
            totalUsers: statistics.totalUsers,
            activeUsers: statistics.activeUsers,
            inactiveUsers: statistics.inactiveUsers,
            lockedAccounts: statistics.lockedAccounts
          }
        }
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get users'
      });
    }
  }

  /**
   * Get user statistics
   * GET /api/users/stats
   */
  async getUserStats(request: FastifyRequest, reply: FastifyReply) {
    try {
      const statistics = await this.userService.getUserStatistics();

      reply.send({
        success: true,
        message: 'User statistics retrieved successfully',
        data: statistics
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get user statistics'
      });
    }
  }

  /**
   * Delete user
   * DELETE /api/users/:id
   */
  async deleteUser(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
    try {
      const success = await this.userService.deleteUserAsync(request.params.id);

      if (!success) {
        reply.status(404).send({
          success: false,
          message: 'User not found'
        });
        return;
      }

      reply.send({
        success: true,
        message: 'User deleted successfully'
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to delete user'
      });
    }
  }

  /**
   * Reset password
   * POST /api/users/reset-password
   */
  async resetPassword(request: FastifyRequest<{ Body: ResetPasswordDto }>, reply: FastifyReply) {
    try {
      const success = await this.userService.resetPasswordAsync(request.body);

      if (!success) {
        reply.status(400).send({
          success: false,
          message: 'Invalid or expired reset token'
        });
        return;
      }

      reply.send({
        success: true,
        message: 'Password reset successfully'
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        reply.status(400).send({
          success: false,
          message: 'Validation failed',
          errors: error.errors
        });
      } else {
        reply.status(500).send({
          success: false,
          message: error instanceof Error ? error.message : 'Failed to reset password'
        });
      }
    }
  }

  /**
   * Verify email
   * POST /api/users/verify-email
   */
  async verifyEmail(request: FastifyRequest<{ Body: VerifyEmailDto }>, reply: FastifyReply) {
    try {
      const success = await this.userService.verifyEmailAsync(request.body);

      if (!success) {
        reply.status(400).send({
          success: false,
          message: 'Invalid verification token or user not found'
        });
        return;
      }

      reply.send({
        success: true,
        message: 'Email verified successfully'
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        reply.status(400).send({
          success: false,
          message: 'Validation failed',
          errors: error.errors
        });
      } else {
        reply.status(500).send({
          success: false,
          message: error instanceof Error ? error.message : 'Failed to verify email'
        });
      }
    }
  }
}
