///////////////////////////////////////////////
/*  Enums for UAParser.js v2.0.5
    https://github.com/faisalman/ua-parser-js
    Author: <PERSON><PERSON><PERSON> <<EMAIL>>
    AGPLv3 License */
//////////////////////////////////////////////

/*jshint esversion: 6 */ 

const BrowserName = Object.freeze({
    '115': '115',
    '2345': '2345',
    '360': '360',
    ALIPAY: 'Alipay',
    AMAYA: 'Amaya',
    ANDROID: 'Android Browser',
    ARORA: 'Arora',
    AVANT: 'Avant',
    AVAST: 'Avast Secure Browser',
    AVG: 'AVG Secure Browser',
    BAIDU: 'Baidu Browser',
    BASILISK: 'Basilisk',
    BLAZER: 'Blazer',
    BOLT: 'Bolt',
    BOWSER: 'Bowser',
    BRAVE: 'Brave',
    CAMINO: 'Camino',
    CHIMERA: 'Chimera',
    CHROME: 'Chrome',
    CHROME_HEADLESS: 'Chrome Headless',
    CHROME_MOBILE: 'Mobile Chrome',
    CHROME_WEBVIEW: 'Chrome WebView',
    CHROMIUM: 'Chromium',
    COBALT: 'Cobalt',
    COC_COC: 'Coc Coc',
    CONKEROR: 'Conkeror',
    DAUM: 'Daum',
    DILLO: 'Dillo',
    DOLPHIN: 'Dolphin',
    DOOBLE: 'Dooble',
    DORIS: 'Doris',
    DRAGON: 'Dragon',
    DUCKDUCKGO: 'DuckDuckGo',
    ECOSIA: 'Ecosia',
    EDGE: 'Edge',
    EDGE_WEBVIEW: 'Edge WebView',
    EDGE_WEBVIEW2: 'Edge WebView2',
    EPIPHANY: 'Epiphany',
    FACEBOOK: 'Facebook',
    FALKON: 'Falkon',
    FIREBIRD: 'Firebird',
    FIREFOX: 'Firefox',
    FIREFOX_FOCUS: 'Firefox Focus',
    FIREFOX_MOBILE: 'Mobile Firefox',
    FIREFOX_REALITY: 'Firefox Reality',
    FENNEC: 'Fennec',
    FLOCK: 'Flock',
    FLOW: 'Flow',
    GO: 'GoBrowser',
    GOOGLE_SEARCH: 'GSA',
    HELIO: 'Helio',
    HEYTAP: 'HeyTap',
    HONOR: 'Honor',
    HUAWEI: 'Huawei Browser',
    ICAB: 'iCab',
    ICE: 'ICE Browser',
    ICEAPE: 'IceApe',
    ICECAT: 'IceCat',
    ICEDRAGON: 'IceDragon',
    ICEWEASEL: 'IceWeasel',
    IE: 'IE',
    INSTAGRAM: 'Instagram',
    IRIDIUM: 'Iridium',
    IRON: 'Iron',
    JASMINE: 'Jasmine',
    KONQUEROR: 'Konqueror',
    KAKAO: 'KakaoTalk',
    KHTML: 'KHTML',
    K_MELEON: 'K-Meleon',
    KLAR: 'Klar',
    KLARNA: 'Klarna',
    KINDLE: 'Kindle',
    LENOVO: 'Smart Lenovo Browser',
    LADYBIRD: 'Ladybird',
    LG: 'LG Browser',
    LIBREWOLF: 'LibreWolf',
    LIEBAO: 'LBBROWSER',
    LINE: 'Line',
    LINKEDIN: 'LinkedIn',
    LINKS: 'Links',
    LUNASCAPE: 'Lunascape',
    LYNX: 'Lynx',
    MAEMO: 'Maemo Browser',
    MAXTHON: 'Maxthon',
    MIDORI: 'Midori',
    MINIMO: 'Minimo',
    MIUI: 'MIUI Browser',
    MOZILLA: 'Mozilla',
    MOSAIC: 'Mosaic',
    NAVER: 'Naver',
    NETFRONT: 'NetFront',
    NETSCAPE: 'Netscape',
    NETSURF: 'Netsurf',
    NOKIA: 'Nokia Browser',
    OBIGO: 'Obigo',
    OCULUS: 'Oculus Browser',
    OMNIWEB: 'OmniWeb',
    OPERA: 'Opera',
    OPERA_COAST: 'Opera Coast',
    OPERA_GX: 'Opera GX',
    OPERA_MINI: 'Opera Mini',
    OPERA_MOBI: 'Opera Mobi',
    OPERA_TABLET: 'Opera Tablet',
    OPERA_TOUCH: 'Opera Touch',
    OTTER: 'Otter',
    OVI: 'OviBrowser',
    PALEMOON: 'PaleMoon',
    PHANTOMJS: 'PhantomJS',
    PHOENIX: 'Phoenix',
    PICOBROWSER: 'Pico Browser',
    POLARIS: 'Polaris',
    PUFFIN: 'Puffin',
    QQ: 'QQBrowser',
    QQ_LITE: 'QQBrowserLite',
    QUARK: 'Quark',
    QUPZILLA: 'QupZilla',
    QUTEBROWSER: 'qutebrowser',
    REKONQ: 'rekonq',
    ROCKMELT: 'Rockmelt',
    SAFARI: 'Safari',
    SAFARI_MOBILE: 'Mobile Safari',
    SAILFISH: 'Sailfish Browser',
    SAMSUNG: 'Samsung Internet',
    SEAMONKEY: 'SeaMonkey',
    SILK: 'Silk',
    SKYFIRE: 'Skyfire',
    SLEIPNIR: 'Sleipnir',
    SLIMBOAT: 'SlimBoat',
    SLIMBROWSER: 'SlimBrowser',
    SLIMJET: 'Slimjet',
    SNAPCHAT: 'Snapchat',
    SOGOU_EXPLORER: 'Sogou Explorer',
    SOGOU_MOBILE: 'Sogou Mobile',
    SURF: 'Surf',
    SWIFTFOX: 'Swiftfox',
    TESLA: 'Tesla',
    TIKTOK: 'TikTok',
    TIZEN: 'Tizen Browser',
    TWITTER: 'Twitter',
    UC: 'UCBrowser',
    UP: 'UP.Browser',
    VIVALDI: 'Vivaldi',
    VIVO: 'Vivo Browser',
    W3M: 'w3m',
    WATERFOX: 'Waterfox',
    WEBKIT: 'WebKit',
    WECHAT: 'WeChat',
    WEIBO: 'Weibo',
    WHALE: 'Whale',
    WOLVIC: 'Wolvic',
    YANDEX: 'Yandex',
    ZALO: 'Zalo'

    // TODO : test!
});
/**
 * @deprecated Use `BrowserName` instead
 */
const Browser = BrowserName;

const BrowserType = Object.freeze({
    CRAWLER: 'crawler',
    CLI: 'cli',
    EMAIL: 'email',
    FETCHER: 'fetcher',
    INAPP: 'inapp',
    MEDIAPLAYER: 'mediaplayer',
    LIBRARY: 'library'
});

const CPUArch = Object.freeze({
    '68K': '68k',
    ALPHA: 'alpha',
    ARM : 'arm',
    ARM_64: 'arm64',
    ARM_HF: 'armhf',
    AVR: 'avr',
    AVR_32: 'avr32',
    IA64: 'ia64',
    IRIX: 'irix',
    IRIX_64: 'irix64',
    MIPS: 'mips',
    MIPS_64: 'mips64',
    PA_RISC: 'pa-risc',
    PPC: 'ppc',
    SPARC: 'sparc',
    SPARC_64: 'sparc64',
    X86: 'ia32',
    X86_64: 'amd64'
});
/**
 * @deprecated Use `CPUArch` instead
 */
const CPU = CPUArch;

const DeviceType = Object.freeze({
    CONSOLE: 'console',
    DESKTOP: 'desktop',
    EMBEDDED: 'embedded',
    MOBILE: 'mobile',
    SMARTTV: 'smarttv',
    TABLET: 'tablet',
    WEARABLE: 'wearable',
    XR: 'xr'
});
/**
 * @deprecated Use `DeviceType` instead
 */
const Device = DeviceType;

const DeviceVendor = Object.freeze({
    ACER: 'Acer',
    ADVAN: 'Advan',
    ALCATEL: 'Alcatel',
    APPLE: 'Apple',
    AMAZON: 'Amazon',
    ARCHOS: 'Archos',
    ASUS: 'ASUS',
    ATT: 'AT&T',
    BENQ: 'BenQ',
    BLACKBERRY: 'BlackBerry',
    BLU: 'BLU',
    CAT: 'Cat',
    DELL: 'Dell',
    ENERGIZER: 'Energizer',
    ESSENTIAL: 'Essential',
    FACEBOOK: 'Facebook',
    FAIRPHONE: 'Fairphone',
    GEEKSPHONE: 'GeeksPhone',
    GENERIC: 'Generic',
    GOOGLE: 'Google',
    HMD: 'HMD',
    HP: 'HP',
    HTC: 'HTC',
    HUAWEI: 'Huawei',
    IMO: 'IMO',
    INFINIX: 'Infinix',
    ITEL: 'itel',
    JOLLA: 'Jolla',
    KOBO: 'Kobo',
    LAVA: 'Lava',
    LENOVO: 'Lenovo',
    LG: 'LG',
    MEIZU: 'Meizu',
    MICROMAX: 'Micromax',
    MICROSOFT: 'Microsoft',
    MOTOROLA: 'Motorola',
    NEXIAN: 'Nexian',
    NINTENDO: 'Nintendo',
    NOKIA: 'Nokia',
    NOTHING: 'Nothing',
    NVIDIA: 'Nvidia',
    ONEPLUS: 'OnePlus',
    OPPO: 'OPPO',
    OUYA: 'Ouya',
    PALM: 'Palm',
    PANASONIC: 'Panasonic',
    PEBBLE: 'Pebble',
    PHILIPS: 'Philips',
    PICO: 'Pico',
    POLYTRON: 'Polytron',
    REALME: 'Realme',
    RETROID: 'Retroid',
    RIM: 'RIM',
    ROKU: 'Roku',
    SAMSUNG: 'Samsung',
    SHARP: 'Sharp',
    SIEMENS: 'Siemens',
    SMARTFREN: 'Smartfren',
    SONY: 'Sony',
    SPRINT: 'Sprint',
    TCL: 'TCL',
    TECHNISAT: 'TechniSAT',
    TECNO: 'TECNO',
    TESLA: 'Tesla',
    ULEFONE: 'Ulefone',
    VIVO: 'Vivo',
    VIZIO: 'Vizio',
    VODAFONE: 'Vodafone',
    XBOX: 'Xbox',
    XIAOMI: 'Xiaomi',
    ZEBRA: 'Zebra',
    ZTE: 'ZTE',

    // TODO : test!
});
/**
 * @deprecated Use `DeviceVendor` instead
 */
const Vendor = DeviceVendor;

const EngineName = Object.freeze({
    AMAYA: 'Amaya',
    ARKWEB: 'ArkWeb',
    BLINK: 'Blink',
    EDGEHTML: 'EdgeHTML',
    FLOW: 'Flow',
    GECKO: 'Gecko',
    GOANNA: 'Goanna',
    ICAB: 'iCab',
    KHTML: 'KHTML',
    LIBWEB: 'LibWeb',
    LINKS: 'Links',
    LYNX: 'Lynx',
    NETFRONT: 'NetFront',
    NETSURF: 'NetSurf',
    PRESTO: 'Presto',
    SERVO: 'Servo',
    TASMAN: 'Tasman',
    TRIDENT: 'Trident',
    W3M: 'w3m',
    WEBKIT: 'WebKit'
});
/**
 * @deprecated Use `EngineName` instead
 */
const Engine = EngineName;

const OSName = Object.freeze({
    AIX: 'AIX',
    AMIGA_OS: 'Amiga OS',
    ANDROID: 'Android',
    ANDROID_X86: 'Android-x86',
    ARCAOS: 'ArcaOS',
    ARCH: 'Arch',
    BADA: 'Bada',
    BEOS: 'BeOS',
    BLACKBERRY: 'BlackBerry',
    CENTOS: 'CentOS',
    CHROME_OS: 'Chrome OS',
    CHROMECAST: 'Chromecast',
    CHROMECAST_ANDROID: 'Chromecast Android',
    CHROMECAST_FUCHSIA: 'Chromecast Fuchsia',
    CHROMECAST_LINUX: 'Chromecast Linux',
    CHROMECAST_SMARTSPEAKER: 'Chromecast SmartSpeaker',
    CONTIKI: 'Contiki',
    DEBIAN: 'Debian',
    DEEPIN: 'Deepin',
    DRAGONFLY: 'DragonFly',
    ELEMENTARY_OS: 'elementary OS',
    FEDORA: 'Fedora',
    FIREFOX_OS: 'Firefox OS',
    FREEBSD: 'FreeBSD',
    FUCHSIA: 'Fuchsia',
    GENTOO: 'Gentoo',
    GHOSTBSD: 'GhostBSD',
    GNU: 'GNU',
    HAIKU: 'Haiku',
    HARMONYOS: 'HarmonyOS',
    HP_UX: 'HP-UX',
    HURD: 'Hurd',
    IOS: 'iOS',
    JOLI: 'Joli',
    KAIOS: 'KaiOS',
    KNOPPIX: 'Knoppix',
    KUBUNTU: 'Kubuntu',
    LINPUS: 'Linpus',
    LINSPIRE: 'Linspire',
    LINUX: 'Linux',
    MACOS: 'macOS',
    MAEMO: 'Maemo',
    MAGEIA: 'Mageia',
    MANDRIVA: 'Mandriva',
    MANJARO: 'Manjaro',
    MEEGO: 'MeeGo',
    MINIX: 'Minix',
    MINT: 'Mint',
    MORPH_OS: 'Morph OS',
    NETBSD: 'NetBSD',
    NETRANGE: 'NetRange',
    NETTV: 'NetTV',
    NINTENDO: 'Nintendo',
    OPENHARMONY: 'OpenHarmony',
    OPENBSD: 'OpenBSD',
    OPENVMS: 'OpenVMS',
    OS2: 'OS/2',
    PALM: 'Palm',
    PC_BSD: 'PC-BSD',
    PCLINUXOS: 'PCLinuxOS',
    PICO: 'Pico',
    PLAN9: 'Plan9',
    PLAYSTATION: 'PlayStation',
    QNX: 'QNX',
    RASPBIAN: 'Raspbian',
    REDHAT: 'RedHat',
    RIM_TABLET_OS: 'RIM Tablet OS',
    RISC_OS: 'RISC OS',
    SABAYON: 'Sabayon',
    SAILFISH: 'Sailfish',
    SERENITYOS: 'SerenityOS',
    SERIES40: 'Series40',
    SLACKWARE: 'Slackware',
    SOLARIS: 'Solaris',
    SUSE: 'SUSE',
    SYMBIAN: 'Symbian',
    TIZEN: 'Tizen',
    UBUNTU: 'Ubuntu',
    UBUNTU_TOUCH: 'Ubuntu Touch',
    UNIX: 'Unix',
    VECTORLINUX: 'VectorLinux',
    WATCHOS: 'watchOS',
    WEBOS: 'WebOS',
    WINDOWS: 'Windows',
    WINDOWS_CE: 'Windows CE',
    WINDOWS_IOT: 'Windows IoT',
    WINDOWS_MOBILE: 'Windows Mobile',
    WINDOWS_PHONE: 'Windows Phone',
    WINDOWS_RT: 'Windows RT',
    XBOX: 'Xbox',
    XUBUNTU: 'Xubuntu',
    ZENWALK: 'Zenwalk'

    // TODO : test!
});
/**
 * @deprecated Use `OSName` instead
 */
const OS = OSName;

/*////////////////////////////////
 * Enums for Extensions submodule
 *///////////////////////////////

const Extension = Object.freeze({
    BrowserName: {
        CLI: {
            CURL: 'curl',
            ELINKS: 'ELinks',
            HTTPIE: 'HTTPie',
            LYNX: 'Lynx',
            WGET: 'Wget'
        },
        Crawler: {
            AHREFS_BOT: 'AhrefsBot',
            AI2_BOT: 'AI2Bot',
            AIHIT_BOT: 'aiHitBot',
            ALGOLIA_CRAWLER: 'Algolia Crawler',
            APPLE_BOT: 'Applebot',
            APPLE_BOT_EXTENDED: 'Applebot-Extended',
            ASK_TEOMA: 'Teoma',
            AMAZON_BOT: 'Amazonbot',
            AMAZON_CONTXBOT: 'contxbot',
            ANTHROPIC_AI: 'anthropic-ai',
            ANTHROPIC_CLAUDE_BOT: 'ClaudeBot',
            ANTHROPIC_CLAUDE_SEARCHBOT: 'Claude-SearchBot',
            ANTHROPIC_CLAUDE_WEB: 'Claude-Web',
            ARCHIVEORG_BOT: 'archive.org_bot',
            BAIDU_ADS: 'Baidu-ADS',
            BAIDU_SPIDER: 'Baiduspider',
            BAIDU_SPIDER_ADS: 'Baiduspider-ads',
            BAIDU_SPIDER_CPRO: 'Baiduspider-cpro',
            BAIDU_SPIDER_FAVO: 'Baiduspider-favo',
            BAIDU_SPIDER_IMAGE: 'Baiduspider-image',
            BAIDU_SPIDER_NEWS: 'Baiduspider-news',
            BAIDU_SPIDER_RENDER: 'Baiduspider-render',
            BAIDU_SPIDER_VIDEO: 'Baiduspider-video',
            BLEX_BOT: 'BLEXBot',
            BOTIFY: 'botify',
            BRAVE_BOT: 'Bravebot',
            BYTEDANCE_BYTESPIDER: 'Bytespider',
            BYTEDANCE_TIKTOKSPIDER: 'TikTokSpider',
            COMMON_CRAWL_CCBOT: 'CCBot',
            COCCOC_BOT_WEB: 'coccocbot-web',
            COCCOC_BOT_IMAGE: 'coccocbot-image',
            COHERE_TRAINING_DATA_CRAWLER: 'cohere-training-data-crawler',
            COTOYOGI: 'Cotoyogi',
            COVEO_BOT: 'Coveobot',
            CRITEO_BOT: 'CriteoBot',
            DATAFORSEO_BOT: 'DataForSeoBot',
            DAUM: 'Daum',
            DAUM_DAUMOA: 'Daumoa',
            DAUM_DAUMOA_IMAGE: 'Daumoa-image',
            DEEPSEEK_BOT: 'DeepSeekBot',
            DIFFBOT: 'Diffbot',
            DUCKDUCKGO_BOT: 'DuckDuckBot',
            DUCKDUCKGO_FAVICONS_BOT: 'DuckDuckGo-Favicons-Bot',
            ELASTIC: 'Elastic',
            EXALEAD_EXABOT: 'Exabot',
            FIRECRAWL_AGENT: 'FirecrawlAgent',
            FREESPOKE: 'Freespoke',
            GOOGLE_ADSBOT: 'AdsBot-Google',
            GOOGLE_ADSBOT_MOBILE: 'Adsbot-Google-Mobile',
            GOOGLE_ADSENSE: 'AdSense',
            GOOGLE_APIS: 'APIs-Google',
            GOOGLE_BOT: 'Googlebot',
            GOOGLE_BOT_IMAGE: 'Googlebot-Image',
            GOOGLE_BOT_NEWS: 'Googlebot-News',
            GOOGLE_BOT_VIDEO: 'Googlebot-Video',
            GOOGLE_CLOUDVERTEXBOT: 'Google-CloudVertexBot',
            GOOGLE_EXTENDED: 'Google-Extended',
            GOOGLE_INSPECTIONTOOL: 'Google-InspectionTool',
            GOOGLE_OTHER: 'GoogleOther',
            GOOGLE_OTHER_IMAGE: 'GoogleOther-Image',
            GOOGLE_OTHER_VIDEO: 'GoogleOther-Video',
            GOOGLE_SAFETY: 'Google-Safety',
            GOOGLE_STOREBOT: 'Storebot-Google',
            HIVE_IMAGESIFTBOT: 'ImagesiftBot',
            HUAWEI_PANGUBOT: 'PanguBot',
            HUAWEI_PETALBOT: 'PetalBot',
            HUGGINGFACE_BOT: 'HuggingFace-Bot',
            HUNTER_VELENPUBLICWEBCRAWLER: 'VelenPublicWebCrawler',
            IA_ARCHIVER: 'ia_archiver',
            IASK_BOT: 'iAskBot',
            KAGI_BOT: 'Kagibot',
            KANGAROO_BOT: 'Kangaroo Bot',
            LINE_SPIDER: 'Linespider',
            LINKEDIN_BOT: 'LinkedInBot',
            MAGPIE_CRAWLER: 'magpie-crawler',
            MARGINALIA: 'marginalia',
            META_EXTERNALAGENT: 'meta-externalagent',
            META_FACEBOOKBOT: 'FacebookBot',
            META_FACEBOOKCATALOG: 'facebookcatalog',
            META_FACEBOOKEXTERNALHIT: 'facebookexternalhit',
            MAJESTIC_MJ12BOT: 'MJ12bot',
            MICROSOFT_BINGBOT: 'Bingbot',
            MICROSOFT_MSNBOT: 'msnbot',
            MICROSOFT_ADIDXBOT: 'adidxbot',
            MOJEEK_BOT: 'MojeekBot',
            MOZ_DOTBOT: 'DotBot',
            ONCRAWL: 'OnCrawl',
            ONESPOT_SCRAPERBOT: 'Onespot-ScraperBot',
            OPENAI_GPTBOT: 'GPTBot',
            OPENAI_SEARCH_BOT: 'OAI-SearchBot',
            PERPLEXITY_BOT: 'PerplexityBot',
            QIHOO_360_SPIDER: '360Spider',
            QWANT_BOT: 'Qwantbot',
            REPLICATE_BOT: 'Replicate-Bot',
            RUNPOD_BOT: 'RunPod-Bot',
            SB_INTUITIONS_BOT: 'SBIntuitionsBot',
            SEEKPORT_BOT: 'SeekportBot',
            SEMRUSH_BOT: 'SemrushBot',
            SEMRUSH_BOT_BACKLINK: 'SemrushBot-BA',
            SEMRUSH_BOT_CONTENTSHAKE: 'SemrushBot-OCOB',
            SEMRUSH_BOT_SEO_CHECKER: 'SemrushBot-SI',
            SEZNAM_BOT: 'SeznamBot',
            SITEIMPROVE: 'Siteimprove',
            SOGOU_PIC_SPIDER: 'Sogou Pic Spider',
            SOGOU_WEB_SPIDER: 'Sogou web spider',
            STARTPAGE: 'Startpage',
            TIMPI_BOT: 'Timpibot',
            TOGETHER_BOT: 'Together-Bot',
            TURNITIN_BOT: 'TurnitinBot',
            TWIN_AGENT: 'TwinAgent',
            VERCEL_V0BOT: 'v0bot',
            WEBZIO: 'webzio',
            WEBZIO_EXTENDED: 'Webzio-Extended',
            WEBZIO_OMGILI: 'omgili',
            WEBZIO_OMGILI_BOT: 'omgilibot',
            XAI_BOT: 'xAI-Bot',
            YAHOO_JAPAN: 'Y!J-BRW',
            YAHOO_SLURP: 'Yahoo! Slurp',
            YANDEX_ACCESSIBILITY_BOT: 'YandexAccessibilityBot',
            YANDEX_ADDITIONAL_BOT: 'YandexAdditionalBot',
            YANDEX_ADNET: 'YandexAdNet',
            YANDEX_BLOGS: 'YandexBlogs',
            YANDEX_BOT: 'YandexBot',
            YANDEX_BOT_MIRRORDETECTOR: 'YandexBot MirrorDetector',
            YANDEX_COMBOT: 'YandexComBot',
            YANDEX_FAVICONS: 'YandexFavicons',
            YANDEX_IMAGE_RESIZER: 'YandexImageResizer',
            YANDEX_IMAGES: 'YandexImages',
            YANDEX_MARKET: 'YandexMarket',
            YANDEX_MEDIA: 'YandexMedia',
            YANDEX_METRIKA: 'YandexMetrika',
            YANDEX_MOBILE_BOT: 'YandexMobileBot',
            YANDEX_MOBILE_SCREENSHOT_BOT: 'YandexMobileScreenShotBot',
            YANDEX_NEWS: 'YandexNews',
            YANDEX_ONTODB: 'YandexOntoDB',
            YANDEX_ONTODB_API: 'YandexOntoDBAPI',
            YANDEX_PARTNER: 'YandexPartner',
            YANDEX_RCA: 'YandexRCA',
            YANDEX_RENDERRESOURCES_BOT: 'YandexRenderResourcesBot',
            YANDEX_SCREENSHOT_BOT: 'YandexScreenshotBot',
            YANDEX_SPRAV_BOT: 'YandexSpravBot',
            YANDEX_TRACKER: 'YandexTracker',
            YANDEX_VERTICALS: 'YandexVerticals',
            YANDEX_VERTIS: 'YandexVertis',
            YANDEX_VIDEO: 'YandexVideo',
            YANDEX_VIDEO_PARSER: 'YandexVideoParser',
            YANDEX_WEBMASTER: 'YandexWebmaster',
            YEP_BOT: 'YepBot',
            YETI: 'Yeti',
            YISOU_SPIDER: 'YisouSpider',
            YOU_BOT: 'YouBot',
            ZHIPU_CHATGLM_SPIDER: 'ChatGLM-Spider',
            ZUM_BOT: 'ZumBot'
        },
        Email: {
            AIRMAIL: 'Airmail',
            APPLE_MAIL: 'Mail',
            BLUEMAIL: 'BlueMail',
            DAUM_MAIL: 'DaumMail',
            EVOLUTION: 'Evolution',
            EM_CLIENT: 'eM Client',
            FOXMAIL: 'Foxmail',
            KMAIL: 'KMail',
            KMAIL2: 'kmail2',
            KONTACT: 'Kontact',
            MICROSOFT_OUTLOOK: 'Microsoft Outlook',
            MICROSOFT_OUTLOOK_MAC: 'MacOutlook',
            NAVER_MAILAPP: 'NaverMailApp',
            POLYMAIL: 'Polymail',
            PROTON_MAIL: 'ProtonMail',
            SPARK_MAIL: 'SparkDesktop',
            SPARROW: 'Sparrow',
            THUNDERBIRD: 'Thunderbird',
            YAHOO_MAIL: 'Yahoo',
            ZIMBRA: 'Zimbra',
            ZOHO_MAIL: 'ZohoMail-Desktop'
        },
        Fetcher: {
            AHREFS_SITEAUDIT: 'AhrefsSiteAudit',
            ANTHROPIC_CLAUDE_USER: 'Claude-User',
            ASANA: 'Asana',
            BETTER_UPTIME_BOT: 'Better Uptime Bot',
            BITLY_BOT: 'bitlybot',
            BLUESKY: 'Bluesky',
            BUFFER_LINKPREVIEWBOT: 'BufferLinkPreviewBot',
            COHERE_AI: 'Cohere-AI',
            DUCKDUCKGO_ASSISTBOT: 'DuckAssistBot',
            GOOGLE_CHROME_LIGHTHOUSE: 'Chrome-Lighthouse',
            GOOGLE_FEEDFETCHER: 'FeedFetcher-Google',
            GOOGLE_GEMINI_DEEP_RESEARCH: 'Gemini-Deep-Research',
            GOOGLE_IMAGEPROXY: 'GoogleImageProxy',
            GOOGLE_PAGERENDERER: 'Google-PageRenderer',
            GOOGLE_READ_ALOUD: 'Google-Read-Aloud',
            GOOGLE_PRODUCER: 'GoogleProducer',
            GOOGLE_SITE_VERIFICATION: 'Google-Site-Verification',
            HUBSPOT_PAGE_FETCHER: 'HubSpot Page Fetcher',
            IFRAMELY: 'Iframely',
            KAKAOTALK_SCRAP: 'kakaotalk-scrap',
            META_EXTERNALFETCHER: 'meta-externalfetcher',
            META_WHATSAPP: 'WhatsApp',
            MICROSOFT_BINGPREVIEW: 'BingPreview',
            MICROSOFT_PREVIEW: 'MicrosoftPreview',
            MISTRALAI_USER: 'MistralAI-User',
            NAVER_BLUENO: 'Blueno',
            ONCRAWL_ROGERBOT: 'rogerbot',
            OPENAI_CHATGPT_USER: 'ChatGPT-User',
            PERPLEXITY_USER: 'Perplexity-User',
            PINTEREST_BOT: 'Pinterestbot',
            SEMRUSH_SITEAUDITBOT: 'SiteAuditBot',
            SNAP_URL_PREVIEW: 'Snap URL Preview',
            SKYPE_URIPREVIEW: 'SkypeUriPreview',
            TELEGRAM_BOT: 'TelegramBot',
            UPTIMEROBOT: 'UptimeRobot',
            VERCEL_FAVICON_BOT: 'vercel-favicon-bot',
            VERCEL_SCREENSHOT_BOT: 'vercel-screenshot-bot',
            VERCEL_BOT: 'Vercelbot',
            VERCEL_FLAGS: 'vercelflags',
            VERCEL_TRACING: 'verceltracing',
            YANDEX_CALENDAR: 'YandexCalendar',
            YANDEX_DIRECT: 'YandexDirect',
            YANDEX_DIRECTDYN: 'YandexDirectDyn',
            YANDEX_DIRECTFETCHER: 'YaDirectFetcher',
            YANDEX_FORDOMAIN: 'YandexForDomain',
            YANDEX_PAGECHECKER: 'YandexPagechecker',
            YANDEX_SEARCHSHOP: 'YandexSearchShop',
            YANDEX_SITELINKS: 'YandexSitelinks',
            YANDEX_USERPROXY: 'YandexUserproxy',
            ZOOMINFO_BOT: 'Zoombot'
        },
        InApp: {
            DISCORD: 'Discord',
            EVERNOTE: 'Evernote',
            FIGMA: 'Figma',
            FLIPBOARD: 'Flipboard',
            MATTERMOST: 'Mattermost',
            TEAMS: 'Teams',
            NOTION: 'Notion',
            POSTMAN: 'Postman',
            RAMBOX: 'Rambox',
            ROCKETCHAT: 'Rocket.Chat',
            SLACK: 'Slack',
            TIKTOK_LITE: 'TikTok Lite',
            VSCODE: 'VS Code',
            YAHOO_JAPAN: 'Yahoo! Japan'
        },
        Library: {
            ADOBE_AIR: 'AdobeAIR',
            AIOHTTP: 'aiohttp',
            APACHE_HTTPCLIENT: 'Apache-HttpClient',
            AXIOS: 'axios',
            GO_HTTP_CLIENT: 'go-http-client',
            GOT: 'got',
            GUZZLEHTTP: 'GuzzleHttp',
            JAVA: 'Java',
            JAVA_HTTPCLIENT: 'Java-http-client',
            JSDOM: 'jsdom',
            LIBWWW_PERL: 'libwww-perl',
            LUA_RESTY_HTTP: 'lua-resty-http',
            NEEDLE: 'Needle',
            NUTCH: 'Nutch',
            OKHTTP: 'OkHttp',
            NODE_FETCH: 'node-fetch',
            NODE_SUPERAGENT: 'node-superagent',
            PHP_SOAP: 'PHP-SOAP',
            POSTMAN_RUNTIME: 'PostmanRuntime',
            PYTHON_HTTPX: 'python-httpx',
            PYTHON_URLLIB: 'python-urllib',
            PYTHON_URLLIB3: 'python-urllib3',
            PYTHON_REQUESTS: 'python-requests',
            SCRAPY: 'Scrapy'
        }
    },
    DeviceVendor: {
        Vehicle: {
            BMW: 'BMW',
            BYD: 'BYD',
            JEEP: 'Jeep',
            RIVIAN: 'Rivian',
            TESLA: 'Tesla',
            VOLVO: 'Volvo'
        }
    }
});

module.exports = { 
    Browser,// deprecated
    CPU,    // deprecated
    Device, // deprecated
    Vendor, // deprecated
    Engine, // deprecated
    OS,     // deprecated
    BrowserName,
    BrowserType, 
    CPUArch,
    DeviceType, 
    DeviceVendor,
    EngineName,
    OSName,
    Extension
};